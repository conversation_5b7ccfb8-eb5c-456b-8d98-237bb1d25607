"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/uretim/page",{

/***/ "(app-pages-browser)/./src/app/tasks/uretim/page.tsx":
/*!***************************************!*\
  !*** ./src/app/tasks/uretim/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UretimPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_add_vaka_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./_components/add-vaka-modal */ \"(app-pages-browser)/./src/app/tasks/uretim/_components/add-vaka-modal.tsx\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useSignalR */ \"(app-pages-browser)/./src/hooks/useSignalR.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Ad ve soyad baş harflerini döndüren yardımcı fonksiyon\nfunction getInitials(fullName) {\n    if (!fullName) return \"\";\n    const [name, surname] = fullName.split(\" \");\n    return ((name === null || name === void 0 ? void 0 : name[0]) || \"\").toUpperCase() + ((surname === null || surname === void 0 ? void 0 : surname[0]) || \"\").toUpperCase();\n}\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nfunction UretimPage() {\n    _s();\n    const [productions, setProductions] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]);\n    const [technicians, setTechnicians] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        total: 0,\n        approved: 0,\n        inProgress: 0,\n        shipped: 0\n    });\n    const [editingNameId, setEditingNameId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingDateId, setEditingDateId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingTargetDateId, setEditingTargetDateId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingTargetDaysId, setEditingTargetDaysId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingReplicationId, setEditingReplicationId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingNotesId, setEditingNotesId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [deleteConfirmId, setDeleteConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [archiveConfirmId, setArchiveConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [assignConfirmId, setAssignConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(10);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(new Date());\n    const [nameModalData, setNameModalData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        name: \"\",\n        surname: \"\"\n    });\n    const [notesModalData, setNotesModalData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempDateValue, setTempDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempTargetDateValue, setTempTargetDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempTargetDaysValue, setTempTargetDaysValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempReplicationValue, setTempReplicationValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const { user } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_9__.useAuthStore)();\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalR)();\n    // Fetch technicians\n    const fetchTechnicians = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchTechnicians]\": async ()=>{\n            try {\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/User/technicians\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setTechnicians(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching technicians:\", error);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchTechnicians]\"], [\n        API_BASE_URL\n    ]);\n    const fetchProductions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchProductions]\": async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/Production\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setProductions(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching productions:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchProductions]\"], [\n        API_BASE_URL\n    ]);\n    const fetchStats = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchStats]\": async ()=>{\n            try {\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/stats\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setStats(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching stats:\", error);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchStats]\"], [\n        API_BASE_URL\n    ]);\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                fetchProductions(),\n                fetchStats()\n            ]);\n            setLastUpdated(new Date());\n            setCountdown(10);\n        }\n    }[\"UretimPage.useCallback[refreshData]\"], [\n        fetchProductions,\n        fetchStats\n    ]);\n    const formatLastUpdated = (date)=>{\n        return date.toLocaleTimeString(\"tr-TR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionCreated\", {\n        \"UretimPage.useSignalREvent\": (production)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>[\n                        ...prev,\n                        production\n                    ]\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Yeni \\xfcretim eklendi: \".concat(production.name, \" \").concat(production.surname));\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionUpdated\", {\n        \"UretimPage.useSignalREvent\": (production)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>prev.map({\n                        \"UretimPage.useSignalREvent\": (p)=>p.id === production.id ? production : p\n                    }[\"UretimPage.useSignalREvent\"])\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"\\xdcretim g\\xfcncellendi: \".concat(production.name, \" \").concat(production.surname));\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionDeleted\", {\n        \"UretimPage.useSignalREvent\": (productionId)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>prev.filter({\n                        \"UretimPage.useSignalREvent\": (p)=>p.id !== productionId\n                    }[\"UretimPage.useSignalREvent\"])\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Üretim silindi\");\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    const updateProduction = async (id, updates)=>{\n        try {\n            const production = productions.find((p)=>p.id === id);\n            if (!production) return false;\n            const updatedProduction = {\n                ...production,\n                ...updates\n            };\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(updatedProduction)\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.map((p)=>p.id === id ? updatedProduction : p));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error updating production:\", error);\n        }\n        return false;\n    };\n    const deleteProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error deleting production:\", error);\n        }\n        return false;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            fetchProductions();\n            fetchStats();\n            fetchTechnicians();\n        }\n    }[\"UretimPage.useEffect\"], [\n        fetchProductions,\n        fetchStats,\n        fetchTechnicians\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            fetchStats();\n        }\n    }[\"UretimPage.useEffect\"], [\n        productions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            let intervalId;\n            if (isAutoRefreshEnabled && countdown > 0) {\n                intervalId = setInterval({\n                    \"UretimPage.useEffect\": ()=>{\n                        setCountdown({\n                            \"UretimPage.useEffect\": (prev)=>prev - 1\n                        }[\"UretimPage.useEffect\"]);\n                    }\n                }[\"UretimPage.useEffect\"], 1000);\n            } else if (isAutoRefreshEnabled && countdown === 0) {\n                refreshData();\n            }\n            return ({\n                \"UretimPage.useEffect\": ()=>{\n                    if (intervalId) clearInterval(intervalId);\n                }\n            })[\"UretimPage.useEffect\"];\n        }\n    }[\"UretimPage.useEffect\"], [\n        isAutoRefreshEnabled,\n        countdown,\n        refreshData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            if (isAutoRefreshEnabled) {\n                setCountdown(10);\n            }\n        }\n    }[\"UretimPage.useEffect\"], [\n        isAutoRefreshEnabled\n    ]);\n    const calculateTargetDays = (targetDate, approvalDate)=>{\n        if (!targetDate || !approvalDate) return null;\n        const target = new Date(targetDate);\n        const approval = new Date(approvalDate);\n        const diffTime = target.getTime() - approval.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const calculateRemainingDays = (targetDate)=>{\n        if (!targetDate) return null;\n        const target = new Date(targetDate);\n        const today = new Date();\n        const diffTime = target.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"tr-TR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    const handleDelete = (id)=>{\n        setDeleteConfirmId(id);\n    };\n    const confirmDelete = async ()=>{\n        if (deleteConfirmId) {\n            const success = await deleteProduction(deleteConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Üretim başarıyla silindi!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim silinemedi!\");\n            }\n            setDeleteConfirmId(null);\n        }\n    };\n    const cancelDelete = ()=>{\n        setDeleteConfirmId(null);\n    };\n    const archiveProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id, \"/archive\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error archiving production:\", error);\n        }\n        return false;\n    };\n    const handleArchive = (id)=>{\n        setArchiveConfirmId(id);\n    };\n    const confirmArchive = async ()=>{\n        if (archiveConfirmId) {\n            const success = await archiveProduction(archiveConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Üretim başarıyla arşivlendi!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim arşivlenemedi!\");\n            }\n            setArchiveConfirmId(null);\n        }\n    };\n    const cancelArchive = ()=>{\n        setArchiveConfirmId(null);\n    };\n    const handleVakaAdded = ()=>{\n        fetchProductions();\n        fetchStats();\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Yeni vaka başarıyla eklendi!\");\n    };\n    const handleCheckboxChange = async (id, field, value)=>{\n        const success = await updateProduction(id, {\n            [field]: value\n        });\n        if (success) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Değişiklik başarıyla kaydedildi!\");\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Değişiklik kaydedilemedi!\");\n        }\n    };\n    const handleNameEdit = (id, name, surname)=>{\n        setEditingNameId(id);\n        setNameModalData({\n            name,\n            surname\n        });\n    };\n    const handleNameSave = async ()=>{\n        if (editingNameId) {\n            const success = await updateProduction(editingNameId, nameModalData);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Ad soyad başarıyla güncellendi!\");\n                setEditingNameId(null);\n                setNameModalData({\n                    name: \"\",\n                    surname: \"\"\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Ad soyad güncellenemedi!\");\n            }\n        }\n    };\n    const handleNameCancel = ()=>{\n        setEditingNameId(null);\n        setNameModalData({\n            name: \"\",\n            surname: \"\"\n        });\n    };\n    const handleDateEdit = (id, currentDate)=>{\n        setEditingDateId(id);\n        setTempDateValue(currentDate || \"\");\n    };\n    const handleDateSave = async ()=>{\n        if (editingDateId) {\n            const success = await updateProduction(editingDateId, {\n                approvalDate: tempDateValue\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Onay tarihi başarıyla güncellendi!\");\n                setEditingDateId(null);\n                setTempDateValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Onay tarihi güncellenemedi!\");\n            }\n        }\n    };\n    const handleDateCancel = ()=>{\n        setEditingDateId(null);\n        setTempDateValue(\"\");\n    };\n    const handleTargetDateEdit = (id, currentDate)=>{\n        setEditingTargetDateId(id);\n        setTempTargetDateValue(currentDate || \"\");\n    };\n    const handleTargetDateSave = async ()=>{\n        if (editingTargetDateId) {\n            const success = await updateProduction(editingTargetDateId, {\n                targetDate: tempTargetDateValue\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Teslimat tarihi başarıyla güncellendi!\");\n                setEditingTargetDateId(null);\n                setTempTargetDateValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Teslimat tarihi güncellenemedi!\");\n            }\n        }\n    };\n    const handleTargetDateCancel = ()=>{\n        setEditingTargetDateId(null);\n        setTempTargetDateValue(\"\");\n    };\n    const handleTargetDaysEdit = (id, currentDays)=>{\n        setEditingTargetDaysId(id);\n        setTempTargetDaysValue(currentDays.toString());\n    };\n    const handleTargetDaysSave = async ()=>{\n        if (editingTargetDaysId) {\n            const days = parseInt(tempTargetDaysValue) || 0;\n            const production = productions.find((p)=>p.id === editingTargetDaysId);\n            if (production) {\n                const startDate = production.approvalDate ? new Date(production.approvalDate) : new Date();\n                const targetDate = new Date(startDate);\n                targetDate.setDate(targetDate.getDate() + days);\n                const targetDateString = targetDate.toISOString().split(\"T\")[0];\n                const success = await updateProduction(editingTargetDaysId, {\n                    targetDate: targetDateString,\n                    ...production.approvalDate ? {} : {\n                        approvalDate: new Date().toISOString().split(\"T\")[0]\n                    }\n                });\n                if (success) {\n                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Hedef gün başarıyla güncellendi!\");\n                    setEditingTargetDaysId(null);\n                    setTempTargetDaysValue(\"\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Hedef gün güncellenemedi!\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim bulunamadı!\");\n            }\n        }\n    };\n    const handleTargetDaysCancel = ()=>{\n        setEditingTargetDaysId(null);\n        setTempTargetDaysValue(\"\");\n    };\n    const handleReplicationEdit = (id, currentValue)=>{\n        setEditingReplicationId(id);\n        setTempReplicationValue(currentValue.toString());\n    };\n    const handleReplicationSave = async ()=>{\n        if (editingReplicationId) {\n            const success = await updateProduction(editingReplicationId, {\n                replication: parseInt(tempReplicationValue) || 0\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Çoğaltma sayısı başarıyla güncellendi!\");\n                setEditingReplicationId(null);\n                setTempReplicationValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Çoğaltma sayısı güncellenemedi!\");\n            }\n        }\n    };\n    const handleReplicationCancel = ()=>{\n        setEditingReplicationId(null);\n        setTempReplicationValue(\"\");\n    };\n    const handleNotesEdit = (id, currentNotes)=>{\n        setEditingNotesId(id);\n        setNotesModalData(currentNotes || \"\");\n    };\n    const handleNotesSave = async ()=>{\n        if (editingNotesId) {\n            const success = await updateProduction(editingNotesId, {\n                notes: notesModalData\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Açıklama başarıyla güncellendi!\");\n                setEditingNotesId(null);\n                setNotesModalData(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Açıklama güncellenemedi!\");\n            }\n        }\n    };\n    const handleNotesCancel = ()=>{\n        setEditingNotesId(null);\n        setNotesModalData(\"\");\n    };\n    const filteredProductions = productions.filter((p)=>p.name.toLowerCase().includes(searchTerm.toLowerCase()) || p.surname.toLowerCase().includes(searchTerm.toLowerCase()) || p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b)=>{\n        const remainingA = calculateRemainingDays(a.targetDate);\n        const remainingB = calculateRemainingDays(b.targetDate);\n        if (remainingA === null && remainingB === null) return 0;\n        if (remainingA === null) return 1;\n        if (remainingB === null) return -1;\n        return remainingA - remainingB;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Y\\xfckleniyor...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n            lineNumber: 577,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleAutoRefresh = ()=>{\n        setIsAutoRefreshEnabled((prev)=>{\n            if (!prev) setCountdown(10);\n            return !prev;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 w-full mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"\\xdcretim Y\\xf6netimi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"\\xdcretim s\\xfcre\\xe7lerini takip edin ve y\\xf6netin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: toggleAutoRefresh,\n                                                        className: \"h-8 w-8 p-0\",\n                                                        children: isAutoRefreshEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: refreshData,\n                                                        className: \"h-8 w-8 p-0\",\n                                                        disabled: loading,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 620,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-xs text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoRefreshEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Yenileme: \",\n                                                                    countdown,\n                                                                    \"s\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Duraklatıldı\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 634,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Son: \",\n                                                            formatLastUpdated(lastUpdated)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 17\n                                            }, this),\n                                            isAutoRefreshEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-1 bg-gray-200 rounded-full overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full bg-blue-500 transition-all duration-1000 ease-linear\",\n                                                    style: {\n                                                        width: \"\".concat((10 - countdown) / 10 * 100, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                    lineNumber: 650,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_add_vaka_modal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                onSuccess: handleVakaAdded,\n                                apiBaseUrl: API_BASE_URL\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 660,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 601,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 593,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Toplam\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 675,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Onaylı\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.approved\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Devam Eden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.inProgress\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"G\\xf6nderilen\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.shipped\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 709,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 708,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Hasta adı, soyadı veya notlarda ara...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 725,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 724,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            children: \"\\xdcretim Listesi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]\",\n                                                        children: \"Ad Soyad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] \",\n                                                        children: \"Onay Kutuları\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 756,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]\",\n                                                        children: \"Onay Tarihi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 759,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"Hedef G\\xfcn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]\",\n                                                        children: \"Teslimat Tarihi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900   w-[20px] max-w-[20px]\",\n                                                        children: \"Kalan G\\xfcn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"cursor-pointer\",\n                                                                            children: \"\\xc7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 775,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"\\xc7oğaltma\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 778,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 777,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 772,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] \",\n                                                        children: \"S\\xfcre\\xe7 Takibi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 783,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"G\\xf6rev Atama\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 786,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                        children: \"A\\xe7ıklama\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 789,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"İşlemler\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 750,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: filteredProductions.map((production, index)=>{\n                                                var _technicians_find, _technicians_find1, _technicians_find2, _technicians_find3;\n                                                const remainingDays = calculateRemainingDays(production.targetDate);\n                                                const targetDaysCount = calculateTargetDays(production.targetDate, production.approvalDate);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50 \".concat(remainingDays !== null ? remainingDays < 0 ? \"border-l-4 border-l-red-700 bg-red-50\" : remainingDays <= 2 && remainingDays >= 0 ? \"animate-pulse border-l-4 border-l-red-500 bg-red-50\" : \"\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-1 text-center border !w-[1px] !max-w-[1px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700 !text-[13px] \",\n                                                                children: index + 1\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 821,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-1  !w-[50px] !max-w-[50px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleNameEdit(production.id, production.name, production.surname) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-900  block truncate\",\n                                                                                    children: (()=>{\n                                                                                        const fullName = \"\".concat(production.name, \" \").concat(production.surname);\n                                                                                        return fullName.length > 15 ? \"\".concat(fullName.substring(0, 15), \"...\") : fullName;\n                                                                                    })()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 847,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 846,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: [\n                                                                                                production.name,\n                                                                                                \" \",\n                                                                                                production.surname\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 858,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"mt-1 text-sm \",\n                                                                                            children: production.notes\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 862,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 857,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 856,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 845,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 844,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  !w-[50px] !max-w-[50px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-3 justify-center  \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center \",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.pStar,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"pStar\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-blue-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 880,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 879,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 878,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"P*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 899,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 898,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 877,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 876,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.greening,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"greening\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-green-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 907,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 906,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 905,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Yeşil\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 926,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 925,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 904,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 903,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.adminApproval,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"adminApproval\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-purple-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 934,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 933,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 932,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Onay\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 953,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 952,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 931,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 930,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[30px] max-w-[30px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 relative \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleDateEdit(production.id, production.approvalDate || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.approvalDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 976,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 962,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[20px] max-w-[20px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleTargetDaysEdit(production.id, targetDaysCount || 0) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: targetDaysCount !== null ? \"\".concat(targetDaysCount, \" g\\xfcn\") : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 998,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 984,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 983,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[20px] max-w-[20px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleTargetDateEdit(production.id, production.targetDate || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.targetDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1008,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[30px] max-w-[30px] \",\n                                                            children: remainingDays !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm px-2 py-1 rounded \".concat(remainingDays < 0 ? \"bg-red-600 text-white\" : remainingDays <= 2 && remainingDays >= 0 ? \"bg-red-500 text-white animate-pulse\" : \"bg-green-100 text-green-800\"),\n                                                                children: remainingDays > 0 ? \"\".concat(remainingDays, \" g\\xfcn\") : remainingDays === 0 ? \"Bugün\" : \"-\".concat(Math.abs(remainingDays), \" g\\xfcn\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1031,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1047,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1029,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[5px] max-w-[5px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 justify-center \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" && production.assignedUserId === (user === null || user === void 0 ? void 0 : user.id) ? ()=>handleReplicationEdit(production.id, production.replication || 0) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-center\",\n                                                                    children: production.replication || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1053,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[50px] max-w-[50px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 justify-center flex-wrap  \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.model,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"model\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-green-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1085,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1084,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1083,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Model\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1106,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1105,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1082,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1081,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.platePressing,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"platePressing\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-yellow-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1114,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1113,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1112,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Plak Basma\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1135,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1134,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1111,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1110,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.fineCut,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"fineCut\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-orange-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1143,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1142,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1141,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"İnce Kesim\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1164,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1163,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1140,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1139,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.packaging,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"packaging\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-purple-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1172,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1171,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1170,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Paketleme\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1193,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1192,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1169,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1168,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.shipping,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"shipping\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-red-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1201,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1200,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1199,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Kargo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1222,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1221,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1198,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1197,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1080,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1079,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[30px] max-w-[30px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? // Admin görünümü - dropdown ile teknisyen seçimi\n                                                                production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: production.assignedUserId,\n                                                                    onValueChange: (newUserId)=>{\n                                                                        if (newUserId === \"none\") {\n                                                                            unassignTask(production.id);\n                                                                        } else {\n                                                                            reassignTask(production.id, newUserId);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                            className: \"w-full h-8 text-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                children: (()=>{\n                                                                                    const tech = technicians.find((t)=>t.id === production.assignedUserId);\n                                                                                    return tech ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2 w-full\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-700 font-bold text-xs\",\n                                                                                                children: getInitials(tech.fullName)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1254,\n                                                                                                columnNumber: 45\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"truncate text-xs\",\n                                                                                                children: tech.fullName\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1257,\n                                                                                                columnNumber: 45\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1253,\n                                                                                        columnNumber: 43\n                                                                                    }, this) : \"Atanmış\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1246,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1245,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                            children: [\n                                                                                technicians.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: tech.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                                                    children: getInitials(tech.fullName)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                    lineNumber: 1271,\n                                                                                                    columnNumber: 43\n                                                                                                }, this),\n                                                                                                tech.fullName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1270,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    }, tech.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1269,\n                                                                                        columnNumber: 39\n                                                                                    }, this)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                    value: \"none\",\n                                                                                    children: \"Atamayı Kaldır\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1278,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1267,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 33\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    onValueChange: (userId)=>{\n                                                                        if (userId) {\n                                                                            reassignTask(production.id, userId);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                            className: \"w-full h-8 text-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                placeholder: \"Teknisyen Se\\xe7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1292,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1291,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                            children: technicians.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                    value: tech.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                                                children: getInitials(tech.fullName)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1298,\n                                                                                                columnNumber: 43\n                                                                                            }, this),\n                                                                                            tech.fullName\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1297,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, tech.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1296,\n                                                                                    columnNumber: 39\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1294,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1284,\n                                                                    columnNumber: 33\n                                                                }, this) : (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" ? // Teknisyen görünümü\n                                                                production.assignedUserId === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>unassignTask(production.id),\n                                                                                    className: \"h-8 w-8 p-0\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-red-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1322,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1314,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1313,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"G\\xf6revi Bırak\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1326,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1325,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1312,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1311,\n                                                                    columnNumber: 33\n                                                                }, this) : !production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>handleTaskAssign(production.id),\n                                                                                    className: \"h-8 w-8 p-0\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1342,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1334,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1333,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"G\\xf6revi Al\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1346,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1345,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1332,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1331,\n                                                                    columnNumber: 33\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                            children: getInitials(((_technicians_find = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find === void 0 ? void 0 : _technicians_find.fullName) || \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1352,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        ((_technicians_find1 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find1 === void 0 ? void 0 : _technicians_find1.fullName) || \"Atanmış\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1351,\n                                                                    columnNumber: 33\n                                                                }, this) : production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 font-bold\",\n                                                                            children: getInitials(((_technicians_find2 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find2 === void 0 ? void 0 : _technicians_find2.fullName) || \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1368,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        ((_technicians_find3 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find3 === void 0 ? void 0 : _technicians_find3.fullName) || \"Atanmış\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1367,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Atanmamış\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1380,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1231,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1230,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleNotesEdit(production.id, production.notes || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden \",\n                                                                                    children: production.notes || \"-\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1406,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1405,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: production.notes\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1412,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1411,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1404,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1403,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1388,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[30px] max-w-[30px] \",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1 justify-center  w-[50px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleArchive(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1430,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1424,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleDelete(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1423,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, production.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 797,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 749,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 748,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 747,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 746,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this),\n            editingNameId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Ad Soyad D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1457,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameModalData.name,\n                                            onChange: (e)=>setNameModalData({\n                                                    ...nameModalData,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Ad girin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1463,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1459,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Soyad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1474,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameModalData.surname,\n                                            onChange: (e)=>setNameModalData({\n                                                    ...nameModalData,\n                                                    surname: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Soyad girin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1477,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1473,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1458,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleNameSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1492,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleNameCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1501,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1496,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1491,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1456,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1455,\n                columnNumber: 9\n            }, this),\n            editingDateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Onay Tarihi D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1513,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: tempDateValue,\n                            onChange: (e)=>setTempDateValue(e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1514,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDateSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1522,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1521,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleDateCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1530,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1520,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1512,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1511,\n                columnNumber: 9\n            }, this),\n            editingTargetDateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Teslimat Tarihi D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1542,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: tempTargetDateValue,\n                            onChange: (e)=>setTempTargetDateValue(e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1545,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleTargetDateSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1553,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1552,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleTargetDateCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1561,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1556,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1551,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1541,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1540,\n                columnNumber: 9\n            }, this),\n            editingTargetDaysId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Hedef G\\xfcn D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1573,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Ka\\xe7 g\\xfcn sonra teslim edilecek?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1575,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: tempTargetDaysValue,\n                                    onChange: (e)=>setTempTargetDaysValue(e.target.value),\n                                    min: \"0\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"G\\xfcn sayısını girin...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1578,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Onay tarihinden itibaren bu kadar g\\xfcn sonra teslimat tarihi otomatik hesaplanacak.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1586,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1574,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleTargetDaysSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1593,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleTargetDaysCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1601,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1596,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1591,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1572,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1571,\n                columnNumber: 9\n            }, this),\n            editingReplicationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"\\xc7oğaltma Sayısı D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1613,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            value: tempReplicationValue,\n                            onChange: (e)=>setTempReplicationValue(e.target.value),\n                            min: \"0\",\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                            placeholder: \"\\xc7oğaltma sayısını girin...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleReplicationSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1626,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1625,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleReplicationCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1634,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1624,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1612,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1611,\n                columnNumber: 9\n            }, this),\n            editingNotesId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"A\\xe7ıklama D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1646,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: notesModalData,\n                            onChange: (e)=>setNotesModalData(e.target.value),\n                            className: \"w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                            placeholder: \"A\\xe7ıklama girin...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1647,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleNotesSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1655,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1654,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleNotesCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1663,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1658,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1653,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1645,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1644,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: deleteConfirmId !== null,\n                onOpenChange: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"\\xdcretimi Sil\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1675,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1676,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1674,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelDelete,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1682,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    children: \"Sil\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1685,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1681,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1673,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1672,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: archiveConfirmId !== null,\n                onOpenChange: cancelArchive,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"\\xdcretimi Arşivle\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1696,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi arşive almak istediğinizden emin misiniz? Arşivlenen \\xfcretimler plan arşiv b\\xf6l\\xfcm\\xfcnde g\\xf6r\\xfcnt\\xfclenebilir.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1697,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1695,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelArchive,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1703,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmArchive,\n                                    children: \"Arşivle\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1706,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1702,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1694,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1693,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: assignConfirmId !== null,\n                onOpenChange: cancelTaskAssign,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"G\\xf6rev Alma Onayı\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu g\\xf6revi almak istediğinizden emin misiniz? G\\xf6rev size atanacak ve s\\xfcre\\xe7 takibi kısmını sadece siz d\\xfczenleyebileceksiniz.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1716,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1714,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelTaskAssign,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1722,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmTaskAssign,\n                                    children: \"G\\xf6revi Al\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1721,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1713,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1712,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n        lineNumber: 591,\n        columnNumber: 5\n    }, this);\n}\n_s(UretimPage, \"xO5dyTApipIzlm7gE7Kg+rQgAd8=\", false, function() {\n    return [\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_9__.useAuthStore,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalR,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent\n    ];\n});\n_c = UretimPage;\nvar _c;\n$RefreshReg$(_c, \"UretimPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/uretim/page.tsx\n"));

/***/ })

});