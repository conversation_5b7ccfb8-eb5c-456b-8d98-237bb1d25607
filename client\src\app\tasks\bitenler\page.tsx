"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Download, Search, Undo } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { useAuthStore } from "@/stores/authStore";
import { apiService } from "@/lib/api";
import type { User } from "@/lib/types";

interface Production {
  id: number;
  pStar: boolean;
  greening: boolean;
  adminApproval: boolean;
  name: string;
  surname: string;
  notes?: string;
  technician: number;
  approvalDate?: string;
  targetDate?: string;
  replication: number;
  model: boolean;
  platePressing: boolean;
  fineCut: boolean;
  packaging: boolean;
  shipping: boolean;
  shippingType?: string;
  isArchived?: boolean;
  assignedUserId?: string;
}

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://***************/api";

export default function BitenlerPage() {
  const [archivedProductions, setArchivedProductions] = useState<Production[]>(
    []
  );
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [unarchiveConfirmId, setUnarchiveConfirmId] = useState<number | null>(
    null
  );
  const { user } = useAuthStore();

  const fetchArchivedProductions = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/Production/archived`);
      if (response.ok) {
        const data = await response.json();
        setArchivedProductions(data);
      }
    } catch (error) {
      console.error("Error fetching archived productions:", error);
    } finally {
      setLoading(false);
    }
  };

  const unarchiveProduction = async (id: number) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/Production/${id}/unarchive`,
        {
          method: "POST",
        }
      );
      if (response.ok) {
        setArchivedProductions((prev) => prev.filter((p) => p.id !== id));
        return true;
      }
    } catch (error) {
      console.error("Error unarchiving production:", error);
    }
    return false;
  };

  const handleUnarchive = (id: number) => {
    setUnarchiveConfirmId(id);
  };

  const confirmUnarchive = async () => {
    if (unarchiveConfirmId) {
      const success = await unarchiveProduction(unarchiveConfirmId);
      if (success) {
        toast.success("Üretim başarıyla arşivden çıkarıldı!");
      } else {
        toast.error("Üretim arşivden çıkarılamadı!");
      }
      setUnarchiveConfirmId(null);
    }
  };

  const cancelUnarchive = () => {
    setUnarchiveConfirmId(null);
  };

  const fetchUsers = async () => {
    try {
      const data = await apiService.getAllUsers();
      setUsers(data);
    } catch (error) {
      console.error("Error fetching users:", error);
    }
  };

  useEffect(() => {
    fetchArchivedProductions();
    fetchUsers();
  }, []);

  const filteredProductions = archivedProductions.filter(
    (p) =>
      p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      p.surname.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const calculateTargetDays = (targetDate?: string, approvalDate?: string) => {
    if (!targetDate || !approvalDate) return null;

    const target = new Date(targetDate);
    const approval = new Date(approvalDate);
    const diffTime = target.getTime() - approval.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const calculateRemainingDays = (targetDate?: string) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Son Yapan'ı bulmak için yardımcı fonksiyon
  function getUserName(userId?: string) {
    if (!userId) return "-";
    const u = users.find((u) => u.id === userId);
    return u ? u.fullName : userId;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6 w-full mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Arşivlenmiş Üretimler
          </h1>
          <p className="text-gray-600 mt-2">
            Arşive alınmış üretimleri görüntüleyin ve yönetin
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Rapor İndir
        </Button>
      </div>

      {/* Stats */}
      <div className="flex justify-between items-center gap-4">
        <div className="w-60">
          <Card className="h-24">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Toplam Arşiv
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {archivedProductions.length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card className="w-full h-24">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Hasta adı, soyadı veya notlarda ara..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Archived List */}
      <Card>
        <CardHeader>
          <CardTitle>Arşivlenmiş Üretimler</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto max-w-full">
            <div className="px-6 py-4">
              <table className="w-full ">
                <thead>
                  <tr className="border-b">
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] "></th>
                    <th className="text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]">
                      Ad Soyad
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] ">
                      Onay Kutuları
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]">
                      Onay Tarihi
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
                      Hedef Gün
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]">
                      Teslimat Tarihi
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900   w-[40px] max-w-[40px]">
                      Tamamlanma Durumu
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] ">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="cursor-pointer">Ç</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Çoğaltma</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] ">
                      Süreç Takibi
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]">
                      Açıklama
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]">
                      Son Yapan
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProductions.map((production, index) => {
                    const remainingDays = calculateRemainingDays(
                      production.targetDate
                    );
                    const targetDaysCount = calculateTargetDays(
                      production.targetDate,
                      production.approvalDate
                    );

                    return (
                      <tr
                        key={production.id}
                        className="border-b hover:bg-gray-50 bg-gray-50/30"
                      >
                        {/* Sıra No */}
                        <td className="py-3 px-1 text-center border !w-[1px] !max-w-[1px]">
                          <span className="font-medium text-gray-700 !text-[13px] ">
                            {index + 1}
                          </span>
                        </td>

                        {/* Ad Soyad */}
                        <td className="py-3 px-1  !w-[50px] !max-w-[50px] ">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span className="text-sm font-medium text-gray-900 cursor-pointer block truncate">
                                  {(() => {
                                    const fullName = `${production.name} ${production.surname}`;
                                    return fullName.length > 15
                                      ? `${fullName.substring(0, 15)}...`
                                      : fullName;
                                  })()}
                                </span>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <div>
                                  <p className="font-medium">
                                    {production.name} {production.surname}
                                  </p>
                                  {production.notes && (
                                    <p className="mt-1 text-sm ">
                                      {production.notes}
                                    </p>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </td>

                        {/* Onay Kutuları */}
                        <td className="py-3 px-2  !w-[50px] !max-w-[50px]  ">
                          <div className="flex gap-3 justify-center  ">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center ">
                                    <input
                                      type="checkbox"
                                      checked={production.pStar}
                                      disabled={true}
                                      className="w-4 h-4 text-blue-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>P*</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.greening}
                                      disabled={true}
                                      className="w-4 h-4 text-green-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Yeşil</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.adminApproval}
                                      disabled={true}
                                      className="w-4 h-4 text-purple-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Onay</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Onay Tarihi */}
                        <td className="py-3 px-2   w-[30px] max-w-[30px] ">
                          <span className="text-sm">
                            {formatDate(production.approvalDate)}
                          </span>
                        </td>

                        {/* Hedef Gün */}
                        <td className="py-3 px-2   w-[20px] max-w-[20px] ">
                          <span className="text-sm">
                            {targetDaysCount !== null
                              ? `${targetDaysCount} gün`
                              : "-"}
                          </span>
                        </td>

                        {/* Teslimat Tarihi */}
                        <td className="py-3 px-2  w-[20px] max-w-[20px] ">
                          <span className="text-sm">
                            {formatDate(production.targetDate)}
                          </span>
                        </td>

                        {/* Tamamlanma Durumu */}
                        <td className="py-3 px-2  w-[30px] max-w-[30px] ">
                          {remainingDays !== null ? (
                            <span
                              className={`text-sm px-2 py-1 rounded ${
                                remainingDays < 0
                                  ? "bg-red-100 text-red-800"
                                  : remainingDays === 0
                                  ? "bg-green-100 text-green-800"
                                  : "bg-blue-100 text-blue-800"
                              }`}
                            >
                              {remainingDays < 0
                                ? `${Math.abs(remainingDays)} gün geç`
                                : remainingDays === 0
                                ? "Zamanında"
                                : `${remainingDays} gün erken`}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-500">-</span>
                          )}
                        </td>

                        {/* Çoğaltma */}
                        <td className="py-3 px-2   w-[5px] max-w-[5px]  ">
                          <div className="flex justify-center  ">
                            <span className="text-sm text-center">
                              {production.replication || 0}
                            </span>
                          </div>
                        </td>

                        {/* Süreç Takibi */}
                        <td className="py-3 px-2  w-[50px] max-w-[50px]  ">
                          <div className="flex gap-2 justify-center flex-wrap  ">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.model}
                                      disabled={true}
                                      className="w-4 h-4 text-green-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Model</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.platePressing}
                                      disabled={true}
                                      className="w-4 h-4 text-yellow-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Plak Basma</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.fineCut}
                                      disabled={true}
                                      className="w-4 h-4 text-orange-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>İnce Kesim</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.packaging}
                                      disabled={true}
                                      className="w-4 h-4 text-purple-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Paketleme</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.shipping}
                                      disabled={true}
                                      className="w-4 h-4 text-red-600"
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Kargo</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Açıklama */}
                        <td className="py-3 px-2 w-[40px] max-w-[40px] ">
                          <div className="flex items-center gap-2 ">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden cursor-help">
                                    {production.notes || "-"}
                                  </span>
                                </TooltipTrigger>
                                {production.notes && (
                                  <TooltipContent className="max-w-xs">
                                    <p>{production.notes}</p>
                                  </TooltipContent>
                                )}
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Son Yapan */}
                        <td className="py-3 px-2 w-[40px] max-w-[40px] ">
                          {getUserName(production.assignedUserId)}
                        </td>

                        {/* İşlemler */}
                        <td className="py-3 px-2 w-[30px] max-w-[30px] ">
                          {user?.role === "Admin" && (
                            <div className="flex gap-1 justify-center  w-[50px]">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleUnarchive(production.id)}
                                className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                              >
                                <Undo className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
              {filteredProductions.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {searchTerm
                    ? "Arama kriterleriyle eşleşen arşivlenmiş üretim bulunamadı."
                    : "Henüz arşivlenmiş üretim bulunmamaktadır."}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Unarchive Confirmation Modal */}
      <Dialog open={unarchiveConfirmId !== null} onOpenChange={cancelUnarchive}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Arşivden Çıkar</DialogTitle>
            <DialogDescription>
              Bu üretimi arşivden çıkarmak istediğinizden emin misiniz? Üretim
              aktif listesine geri döndürülecek.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelUnarchive}>
              İptal
            </Button>
            <Button onClick={confirmUnarchive}>Arşivden Çıkar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
