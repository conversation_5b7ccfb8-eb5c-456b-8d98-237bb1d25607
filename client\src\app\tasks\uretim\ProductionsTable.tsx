import React from "react";
import {
  Toolt<PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Production {
  id: number;
  pStar: boolean;
  greening: boolean;
  adminApproval: boolean;
  name: string;
  surname: string;
  notes?: string;
  technician: number;
  approvalDate?: string;
  targetDate?: string;
  replication: number;
  model: boolean;
  platePressing: boolean;
  fineCut: boolean;
  packaging: boolean;
  shipping: boolean;
  shippingType?: string;
  isArchived?: boolean;
  assignedUserId?: string;
  assignedAt?: string;
  startedAt?: string;
  completedAt?: string;
  isAssigned?: boolean;
}

interface Technician {
  id: string;
  fullName: string;
  email: string;
  role: string;
}

interface ProductionsTableProps {
  productions: Production[];
  user: any;
  technicians: Technician[];
  getInitials: (fullName: string) => string;
  calculateRemainingDays: (targetDate?: string) => number | null;
  calculateTargetDays: (
    targetDate?: string,
    approvalDate?: string
  ) => number | null;
  formatDate: (dateString?: string) => string;
  handleNameEdit: (id: number, name: string, surname: string) => void;
  handleCheckboxChange: (
    id: number,
    field: keyof Production,
    value: boolean
  ) => void;
  handleDateEdit: (id: number, currentDate: string) => void;
  handleTargetDaysEdit: (id: number, currentDays: number) => void;
  handleTargetDateEdit: (id: number, currentDate: string) => void;
  handleReplicationEdit: (id: number, currentValue: number) => void;
  handleNotesEdit: (id: number, currentNotes: string) => void;
  handleArchive: (id: number) => void;
  handleDelete: (id: number) => void;
  unassignTask: (productionId: number) => void;
  reassignTask: (productionId: number, newUserId: string) => void;
  handleTaskAssign: (id: number) => void;
}

const ProductionsTable: React.FC<ProductionsTableProps> = ({
  productions,
  user,
  technicians,
  getInitials,
  calculateRemainingDays,
  calculateTargetDays,
  formatDate,
  handleNameEdit,
  handleCheckboxChange,
  handleDateEdit,
  handleTargetDaysEdit,
  handleTargetDateEdit,
  handleReplicationEdit,
  handleNotesEdit,
  handleArchive,
  handleDelete,
  unassignTask,
  reassignTask,
  handleTaskAssign,
}) => {
  return (
    <table className="w-full ">
      <thead>
        <tr className="border-b">
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] "></th>
          <th className="text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]">
            Ad Soyad
          </th>
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] ">
            Onay Kutuları
          </th>
          <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]">
            Onay Tarihi
          </th>
          <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
            Hedef Gün
          </th>
          <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]">
            Teslimat Tarihi
          </th>
          <th className="text-left text-sm py-3 px-2 font-medium text-gray-900   w-[20px] max-w-[20px]">
            Kalan Gün
          </th>
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] ">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="cursor-pointer">Ç</span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Çoğaltma</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </th>
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] ">
            Süreç Takibi
          </th>
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
            Görev Atama
          </th>
          <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]">
            Açıklama
          </th>
          <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
            İşlemler
          </th>
        </tr>
      </thead>
      <tbody>
        {productions.map((production, index) => {
          const remainingDays = calculateRemainingDays(production.targetDate);
          const targetDaysCount = calculateTargetDays(
            production.targetDate,
            production.approvalDate
          );
          // ... (tbody içeriği buraya taşınacak)
          return null; // Geçici olarak null, devamında tbody içeriği eklenecek
        })}
      </tbody>
    </table>
  );
};

export default ProductionsTable;
