"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Trash2,
  Save,
  X,
  Check,
  Calendar,
  Package,
  Truck,
  Archive,
  UserCheck,
  UserX,
  RefreshCw,
  Play,
  Pause,
  Clock,
} from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import AddVakaModal from "./_components/add-vaka-modal";
import { useAuthStore } from "@/stores/authStore";
import { useSignalR, useSignalREvent } from "@/hooks/useSignalR";

interface Production {
  id: number;
  pStar: boolean;
  greening: boolean;
  adminApproval: boolean;
  name: string;
  surname: string;
  notes?: string;
  technician: number;
  approvalDate?: string;
  targetDate?: string;
  replication: number;
  model: boolean;
  platePressing: boolean;
  fineCut: boolean;
  packaging: boolean;
  shipping: boolean;
  shippingType?: string;
  isArchived?: boolean;
  // Task Assignment Fields
  assignedUserId?: string;
  assignedAt?: string;
  startedAt?: string;
  completedAt?: string;
  isAssigned?: boolean;
}

interface Technician {
  id: string;
  fullName: string;
  email: string;
  role: string;
}

// Ad ve soyad baş harflerini döndüren yardımcı fonksiyon
function getInitials(fullName: string) {
  if (!fullName) return "";
  const [name, surname] = fullName.split(" ");
  return (name?.[0] || "").toUpperCase() + (surname?.[0] || "").toUpperCase();
}

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://***************/api";

export default function UretimPage() {
  const [productions, setProductions] = useState<Production[]>([]);
  const [technicians, setTechnicians] = useState<Technician[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    approved: 0,
    inProgress: 0,
    shipped: 0,
  });

  const [editingNameId, setEditingNameId] = useState<number | null>(null);
  const [editingDateId, setEditingDateId] = useState<number | null>(null);
  const [editingTargetDateId, setEditingTargetDateId] = useState<number | null>(
    null
  );
  const [editingTargetDaysId, setEditingTargetDaysId] = useState<number | null>(
    null
  );
  const [editingReplicationId, setEditingReplicationId] = useState<
    number | null
  >(null);
  const [editingNotesId, setEditingNotesId] = useState<number | null>(null);

  const [deleteConfirmId, setDeleteConfirmId] = useState<number | null>(null);
  const [archiveConfirmId, setArchiveConfirmId] = useState<number | null>(null);
  const [assignConfirmId, setAssignConfirmId] = useState<number | null>(null);

  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  const [countdown, setCountdown] = useState(10);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const [nameModalData, setNameModalData] = useState({ name: "", surname: "" });
  const [notesModalData, setNotesModalData] = useState("");
  const [tempDateValue, setTempDateValue] = useState("");
  const [tempTargetDateValue, setTempTargetDateValue] = useState("");
  const [tempTargetDaysValue, setTempTargetDaysValue] = useState("");
  const [tempReplicationValue, setTempReplicationValue] = useState("");

  const { user } = useAuthStore();

  useSignalR();

  // Fetch technicians
  const fetchTechnicians = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/User/technicians`);
      if (response.ok) {
        const data = await response.json();
        setTechnicians(data);
      }
    } catch (error) {
      console.error("Error fetching technicians:", error);
    }
  }, [API_BASE_URL]);
   // Task assignment functions
  const assignTask = async (productionId: number) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/Production/${productionId}/assign`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(user?.id),
        }
      );

      if (response.ok) {
        const updatedProduction = await response.json();
        setProductions((prev) =>
          prev.map((p) => (p.id === productionId ? updatedProduction : p))
        );
        toast.success("Görev başarıyla alındı!");
      } else {
        const errorData = await response.text();
        toast.error(errorData || "Görev alınamadı!");
      }
    } catch (error) {
      console.error("Error assigning task:", error);
      toast.error("Görev alınamadı!");
    }
  };

  const unassignTask = async (productionId: number) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/Production/${productionId}/unassign`,
        {
          method: "POST",
        }
      );

      if (response.ok) {
        const updatedProduction = await response.json();
        setProductions((prev) =>
          prev.map((p) => (p.id === productionId ? updatedProduction : p))
        );
        toast.success("Görev başarıyla bırakıldı!");
      } else {
        toast.error("Görev bırakılamadı!");
      }
    } catch (error) {
      console.error("Error unassigning task:", error);
      toast.error("Görev bırakılamadı!");
    }
  };

  const reassignTask = async (productionId: number, newUserId: string) => {
    try {
      // First unassign, then assign to new user
      await unassignTask(productionId);

      const response = await fetch(
        `${API_BASE_URL}/Production/${productionId}/assign`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(newUserId),
        }
      );

      if (response.ok) {
        const updatedProduction = await response.json();
        setProductions((prev) =>
          prev.map((p) => (p.id === productionId ? updatedProduction : p))
        );
        toast.success("Görev başarıyla yeniden atandı!");
      } else {
        toast.error("Görev yeniden atanamadı!");
      }
    } catch (error) {
      console.error("Error reassigning task:", error);
      toast.error("Görev yeniden atanamadı!");
    }
  };

  const handleTaskAssign = (id: number) => {
    setAssignConfirmId(id);
  };

  const confirmTaskAssign = async () => {
    if (assignConfirmId && user?.id) {
      await assignTask(assignConfirmId);
      setAssignConfirmId(null);
    }
  };

  const cancelTaskAssign = () => {
    setAssignConfirmId(null);
  };


  const fetchProductions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/Production`);
      if (response.ok) {
        const data = await response.json();
        setProductions(data);
      }
    } catch (error) {
      console.error("Error fetching productions:", error);
    } finally {
      setLoading(false);
    }
  }, [API_BASE_URL]);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/Production/stats`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  }, [API_BASE_URL]);

  const refreshData = useCallback(async () => {
    await Promise.all([fetchProductions(), fetchStats()]);
    setLastUpdated(new Date());
    setCountdown(10);
  }, [fetchProductions, fetchStats]);

  const formatLastUpdated = (date: Date) => {
    return date.toLocaleTimeString("tr-TR", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  useSignalREvent<Production>("ProductionCreated", (production) => {
    setProductions((prev) => [...prev, production]);
    fetchStats();
    toast.success(
      `Yeni üretim eklendi: ${production.name} ${production.surname}`
    );
  });

  useSignalREvent<Production>("ProductionUpdated", (production) => {
    setProductions((prev) =>
      prev.map((p) => (p.id === production.id ? production : p))
    );
    fetchStats();
    toast.info(`Üretim güncellendi: ${production.name} ${production.surname}`);
  });

  useSignalREvent<number>("ProductionDeleted", (productionId) => {
    setProductions((prev) => prev.filter((p) => p.id !== productionId));
    fetchStats();
    toast.info("Üretim silindi");
  });

  const updateProduction = async (id: number, updates: Partial<Production>) => {
    try {
      const production = productions.find((p) => p.id === id);
      if (!production) return false;

      const updatedProduction = { ...production, ...updates };

      const response = await fetch(`${API_BASE_URL}/Production/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updatedProduction),
      });
      if (response.ok) {
        setProductions((prev) =>
          prev.map((p) => (p.id === id ? updatedProduction : p))
        );
        return true;
      }
    } catch (error) {
      console.error("Error updating production:", error);
    }
    return false;
  };

  const deleteProduction = async (id: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/Production/${id}`, {
        method: "DELETE",
      });
      if (response.ok) {
        setProductions((prev) => prev.filter((p) => p.id !== id));
        return true;
      }
    } catch (error) {
      console.error("Error deleting production:", error);
    }
    return false;
  };

  useEffect(() => {
    fetchProductions();
    fetchStats();
    fetchTechnicians();
  }, [fetchProductions, fetchStats, fetchTechnicians]);

  useEffect(() => {
    fetchStats();
  }, [productions]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isAutoRefreshEnabled && countdown > 0) {
      intervalId = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (isAutoRefreshEnabled && countdown === 0) {
      refreshData();
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isAutoRefreshEnabled, countdown, refreshData]);

  useEffect(() => {
    if (isAutoRefreshEnabled) {
      setCountdown(10);
    }
  }, [isAutoRefreshEnabled]);

  const calculateTargetDays = (targetDate?: string, approvalDate?: string) => {
    if (!targetDate || !approvalDate) return null;

    const target = new Date(targetDate);
    const approval = new Date(approvalDate);
    const diffTime = target.getTime() - approval.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const calculateRemainingDays = (targetDate?: string) => {
    if (!targetDate) return null;
    const target = new Date(targetDate);
    const today = new Date();
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    return date.toLocaleDateString("tr-TR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleDelete = (id: number) => {
    setDeleteConfirmId(id);
  };

  const confirmDelete = async () => {
    if (deleteConfirmId) {
      const success = await deleteProduction(deleteConfirmId);
      if (success) {
        toast.success("Üretim başarıyla silindi!");
      } else {
        toast.error("Üretim silinemedi!");
      }
      setDeleteConfirmId(null);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const archiveProduction = async (id: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/Production/${id}/archive`, {
        method: "POST",
      });
      if (response.ok) {
        setProductions((prev) => prev.filter((p) => p.id !== id));
        return true;
      }
    } catch (error) {
      console.error("Error archiving production:", error);
    }
    return false;
  };

  const handleArchive = (id: number) => {
    setArchiveConfirmId(id);
  };

  const confirmArchive = async () => {
    if (archiveConfirmId) {
      const success = await archiveProduction(archiveConfirmId);
      if (success) {
        toast.success("Üretim başarıyla arşivlendi!");
      } else {
        toast.error("Üretim arşivlenemedi!");
      }
      setArchiveConfirmId(null);
    }
  };

  const cancelArchive = () => {
    setArchiveConfirmId(null);
  };

  const handleVakaAdded = () => {
    fetchProductions();
    fetchStats();
    toast.success("Yeni vaka başarıyla eklendi!");
  };

  const handleCheckboxChange = async (
    id: number,
    field: keyof Production,
    value: boolean
  ) => {
    const success = await updateProduction(id, { [field]: value });
    if (success) {
      toast.success("Değişiklik başarıyla kaydedildi!");
    } else {
      toast.error("Değişiklik kaydedilemedi!");
    }
  };

  const handleNameEdit = (id: number, name: string, surname: string) => {
    setEditingNameId(id);
    setNameModalData({ name, surname });
  };

  const handleNameSave = async () => {
    if (editingNameId) {
      const success = await updateProduction(editingNameId, nameModalData);
      if (success) {
        toast.success("Ad soyad başarıyla güncellendi!");
        setEditingNameId(null);
        setNameModalData({ name: "", surname: "" });
      } else {
        toast.error("Ad soyad güncellenemedi!");
      }
    }
  };

  const handleNameCancel = () => {
    setEditingNameId(null);
    setNameModalData({ name: "", surname: "" });
  };

  const handleDateEdit = (id: number, currentDate: string) => {
    setEditingDateId(id);
    setTempDateValue(currentDate || "");
  };

  const handleDateSave = async () => {
    if (editingDateId) {
      const success = await updateProduction(editingDateId, {
        approvalDate: tempDateValue,
      });
      if (success) {
        toast.success("Onay tarihi başarıyla güncellendi!");
        setEditingDateId(null);
        setTempDateValue("");
      } else {
        toast.error("Onay tarihi güncellenemedi!");
      }
    }
  };

  const handleDateCancel = () => {
    setEditingDateId(null);
    setTempDateValue("");
  };

  const handleTargetDateEdit = (id: number, currentDate: string) => {
    setEditingTargetDateId(id);
    setTempTargetDateValue(currentDate || "");
  };

  const handleTargetDateSave = async () => {
    if (editingTargetDateId) {
      const success = await updateProduction(editingTargetDateId, {
        targetDate: tempTargetDateValue,
      });
      if (success) {
        toast.success("Teslimat tarihi başarıyla güncellendi!");
        setEditingTargetDateId(null);
        setTempTargetDateValue("");
      } else {
        toast.error("Teslimat tarihi güncellenemedi!");
      }
    }
  };

  const handleTargetDateCancel = () => {
    setEditingTargetDateId(null);
    setTempTargetDateValue("");
  };

  const handleTargetDaysEdit = (id: number, currentDays: number) => {
    setEditingTargetDaysId(id);
    setTempTargetDaysValue(currentDays.toString());
  };

  const handleTargetDaysSave = async () => {
    if (editingTargetDaysId) {
      const days = parseInt(tempTargetDaysValue) || 0;
      const production = productions.find((p) => p.id === editingTargetDaysId);

      if (production) {
        const startDate = production.approvalDate
          ? new Date(production.approvalDate)
          : new Date();

        const targetDate = new Date(startDate);
        targetDate.setDate(targetDate.getDate() + days);

        const targetDateString = targetDate.toISOString().split("T")[0];

        const success = await updateProduction(editingTargetDaysId, {
          targetDate: targetDateString,
          ...(production.approvalDate
            ? {}
            : { approvalDate: new Date().toISOString().split("T")[0] }),
        });

        if (success) {
          toast.success("Hedef gün başarıyla güncellendi!");
          setEditingTargetDaysId(null);
          setTempTargetDaysValue("");
        } else {
          toast.error("Hedef gün güncellenemedi!");
        }
      } else {
        toast.error("Üretim bulunamadı!");
      }
    }
  };

  const handleTargetDaysCancel = () => {
    setEditingTargetDaysId(null);
    setTempTargetDaysValue("");
  };

  const handleReplicationEdit = (id: number, currentValue: number) => {
    setEditingReplicationId(id);
    setTempReplicationValue(currentValue.toString());
  };

  const handleReplicationSave = async () => {
    if (editingReplicationId) {
      const success = await updateProduction(editingReplicationId, {
        replication: parseInt(tempReplicationValue) || 0,
      });
      if (success) {
        toast.success("Çoğaltma sayısı başarıyla güncellendi!");
        setEditingReplicationId(null);
        setTempReplicationValue("");
      } else {
        toast.error("Çoğaltma sayısı güncellenemedi!");
      }
    }
  };

  const handleReplicationCancel = () => {
    setEditingReplicationId(null);
    setTempReplicationValue("");
  };

  const handleNotesEdit = (id: number, currentNotes: string) => {
    setEditingNotesId(id);
    setNotesModalData(currentNotes || "");
  };

  const handleNotesSave = async () => {
    if (editingNotesId) {
      const success = await updateProduction(editingNotesId, {
        notes: notesModalData,
      });
      if (success) {
        toast.success("Açıklama başarıyla güncellendi!");
        setEditingNotesId(null);
        setNotesModalData("");
      } else {
        toast.error("Açıklama güncellenemedi!");
      }
    }
  };

  const handleNotesCancel = () => {
    setEditingNotesId(null);
    setNotesModalData("");
  };

  const filteredProductions = productions
    .filter(
      (p) =>
        p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.surname.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      const remainingA = calculateRemainingDays(a.targetDate);
      const remainingB = calculateRemainingDays(b.targetDate);

      if (remainingA === null && remainingB === null) return 0;
      if (remainingA === null) return 1;
      if (remainingB === null) return -1;

      return remainingA - remainingB;
    });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Yükleniyor...</div>
      </div>
    );
  }

  const toggleAutoRefresh = () => {
    setIsAutoRefreshEnabled((prev) => {
      if (!prev) setCountdown(10);
      return !prev;
    });
  };

  return (
    <div className="space-y-6 w-full mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Üretim Yönetimi</h1>
          <p className="text-gray-600 mt-2">
            Üretim süreçlerini takip edin ve yönetin
          </p>
        </div>

        <div className="flex items-center gap-4">
          {/* Auto Refresh Widget */}
          <Card className="border-0 shadow-sm">
            <CardContent className="p-3">
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleAutoRefresh}
                    className="h-8 w-8 p-0"
                  >
                    {isAutoRefreshEnabled ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshData}
                    className="h-8 w-8 p-0"
                    disabled={loading}
                  >
                    <RefreshCw
                      className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                    />
                  </Button>
                </div>

                <div className="flex flex-col items-center">
                  <div className="flex items-center gap-1 text-xs text-gray-600">
                    <Clock className="h-3 w-3" />
                    {isAutoRefreshEnabled ? (
                      <span>Yenileme: {countdown}s</span>
                    ) : (
                      <span>Duraklatıldı</span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    Son: {formatLastUpdated(lastUpdated)}
                  </div>
                </div>

                {/* Progress Bar */}
                {isAutoRefreshEnabled && (
                  <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 transition-all duration-1000 ease-linear"
                      style={{ width: `${((10 - countdown) / 10) * 100}%` }}
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <AddVakaModal onSuccess={handleVakaAdded} apiBaseUrl={API_BASE_URL} />
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Toplam</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.total}
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Onaylı</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.approved}
                </p>
              </div>
              <Check className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Devam Eden</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.inProgress}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Gönderilen</p>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.shipped}
                </p>
              </div>
              <Truck className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Hasta adı, soyadı veya notlarda ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Production Table */}
      <Card>
        <CardHeader>
          <CardTitle>Üretim Listesi</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto max-w-full">
            <div className="px-6 py-4">
              <table className="w-full ">
                <thead>
                  <tr className="border-b">
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] "></th>
                    <th className="text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]">
                      Ad Soyad
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] ">
                      Onay Kutuları
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]">
                      Onay Tarihi
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
                      Hedef Gün
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]">
                      Teslimat Tarihi
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900   w-[20px] max-w-[20px]">
                      Kalan Gün
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] ">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="cursor-pointer">Ç</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Çoğaltma</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] ">
                      Süreç Takibi
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
                      Görev Atama
                    </th>
                    <th className="text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]">
                      Açıklama
                    </th>
                    <th className="text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]">
                      İşlemler
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProductions.map((production, index) => {
                    const remainingDays = calculateRemainingDays(
                      production.targetDate
                    );
                    const targetDaysCount = calculateTargetDays(
                      production.targetDate,
                      production.approvalDate
                    );

                    return (
                      <tr
                        key={production.id}
                        className={`border-b hover:bg-gray-50 ${
                          remainingDays !== null
                            ? remainingDays < 0
                              ? "border-l-4 border-l-red-700 bg-red-50"
                              : remainingDays <= 2 && remainingDays >= 0
                              ? "animate-pulse border-l-4 border-l-red-500 bg-red-50"
                              : ""
                            : ""
                        }`}
                      >
                        {/* Sıra No */}
                        <td className="py-3 px-1 text-center border !w-[1px] !max-w-[1px]">
                          <span className="font-medium text-gray-700 !text-[13px] ">
                            {index + 1}
                          </span>
                        </td>

                        {/* Ad Soyad */}
                        <td className="py-3 px-1  !w-[50px] !max-w-[50px] ">
                          <div
                            className={`flex items-center gap-2 ${
                              user?.role === "Admin" ? "cursor-pointer" : ""
                            }`}
                            onClick={
                              user?.role === "Admin"
                                ? () =>
                                    handleNameEdit(
                                      production.id,
                                      production.name,
                                      production.surname
                                    )
                                : undefined
                            }
                          >
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-sm font-medium text-gray-900  block truncate">
                                    {(() => {
                                      const fullName = `${production.name} ${production.surname}`;
                                      return fullName.length > 15
                                        ? `${fullName.substring(0, 15)}...`
                                        : fullName;
                                    })()}
                                  </span>
                                </TooltipTrigger>
                                <TooltipContent className="max-w-xs">
                                  <div>
                                    <p className="font-medium">
                                      {production.name} {production.surname}
                                    </p>
                                    {production.notes && (
                                      <p className="mt-1 text-sm ">
                                        {production.notes}
                                      </p>
                                    )}
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Onay Kutuları */}
                        <td className="py-3 px-2  !w-[50px] !max-w-[50px]  ">
                          <div className="flex gap-3 justify-center  ">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center ">
                                    <input
                                      type="checkbox"
                                      checked={production.pStar}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "pStar",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-blue-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        user?.role !== "Software"
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>P*</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.greening}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "greening",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-green-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        user?.role !== "Software"
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Yeşil</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.adminApproval}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "adminApproval",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-purple-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        user?.role !== "Software"
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Onay</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Onay Tarihi */}
                        <td className="py-3 px-2   w-[30px] max-w-[30px] ">
                          <div
                            className={`flex items-center gap-2 relative ${
                              user?.role === "Admin" ? "cursor-pointer" : ""
                            }`}
                            onClick={
                              user?.role === "Admin"
                                ? () =>
                                    handleDateEdit(
                                      production.id,
                                      production.approvalDate || ""
                                    )
                                : undefined
                            }
                          >
                            <span className="text-sm">
                              {formatDate(production.approvalDate)}
                            </span>
                          </div>
                        </td>

                        {/* Hedef Gün */}
                        <td className="py-3 px-2   w-[20px] max-w-[20px] ">
                          <div
                            className={`flex items-center gap-2 ${
                              user?.role === "Admin" ? "cursor-pointer" : ""
                            }`}
                            onClick={
                              user?.role === "Admin"
                                ? () =>
                                    handleTargetDaysEdit(
                                      production.id,
                                      targetDaysCount || 0
                                    )
                                : undefined
                            }
                          >
                            <span className="text-sm">
                              {targetDaysCount !== null
                                ? `${targetDaysCount} gün`
                                : "-"}
                            </span>
                          </div>
                        </td>

                        {/* Teslimat Tarihi */}
                        <td className="py-3 px-2  w-[20px] max-w-[20px] ">
                          <div
                            className={`flex items-center gap-2 ${
                              user?.role === "Admin" ? "cursor-pointer" : ""
                            }`}
                            onClick={
                              user?.role === "Admin"
                                ? () =>
                                    handleTargetDateEdit(
                                      production.id,
                                      production.targetDate || ""
                                    )
                                : undefined
                            }
                          >
                            <span className="text-sm">
                              {formatDate(production.targetDate)}
                            </span>
                          </div>
                        </td>

                        {/* Kalan Gün */}
                        <td className="py-3 px-2  w-[30px] max-w-[30px] ">
                          {remainingDays !== null ? (
                            <span
                              className={`text-sm px-2 py-1 rounded ${
                                remainingDays < 0
                                  ? "bg-red-600 text-white"
                                  : remainingDays <= 2 && remainingDays >= 0
                                  ? "bg-red-500 text-white animate-pulse"
                                  : "bg-green-100 text-green-800"
                              }`}
                            >
                              {remainingDays > 0
                                ? `${remainingDays} gün`
                                : remainingDays === 0
                                ? "Bugün"
                                : `-${Math.abs(remainingDays)} gün`}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-500">-</span>
                          )}
                        </td>

                        {/* Çoğaltma */}
                        <td className="py-3 px-2   w-[5px] max-w-[5px]  ">
                          <div
                            className={`flex items-center gap-2 justify-center ${
                              user?.role === "Admin" ||
                              user?.role === "Technician"
                                ? "cursor-pointer"
                                : ""
                            }`}
                            onClick={
                              user?.role === "Admin" ||
                              (user?.role === "Technician" &&
                                production.assignedUserId === user?.id)
                                ? () =>
                                    handleReplicationEdit(
                                      production.id,
                                      production.replication || 0
                                    )
                                : undefined
                            }
                          >
                            <span className="text-sm text-center">
                              {production.replication || 0}
                            </span>
                          </div>
                        </td>

                        {/* Süreç Takibi */}
                        <td className="py-3 px-2  w-[50px] max-w-[50px]  ">
                          <div className="flex gap-2 justify-center flex-wrap  ">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.model}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "model",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-green-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        (user?.role !== "Technician" ||
                                          production.assignedUserId !==
                                            user?.id)
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Model</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.platePressing}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "platePressing",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-yellow-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        (user?.role !== "Technician" ||
                                          production.assignedUserId !==
                                            user?.id)
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Plak Basma</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.fineCut}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "fineCut",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-orange-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        (user?.role !== "Technician" ||
                                          production.assignedUserId !==
                                            user?.id)
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>İnce Kesim</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.packaging}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "packaging",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-purple-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        (user?.role !== "Technician" ||
                                          production.assignedUserId !==
                                            user?.id)
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Paketleme</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <div className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={production.shipping}
                                      onChange={(e) =>
                                        handleCheckboxChange(
                                          production.id,
                                          "shipping",
                                          e.target.checked
                                        )
                                      }
                                      className="w-4 h-4 text-red-600"
                                      disabled={
                                        user?.role !== "Admin" &&
                                        (user?.role !== "Technician" ||
                                          production.assignedUserId !==
                                            user?.id)
                                      }
                                    />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Kargo</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* Görev Atama */}
                        <td className="py-3 px-2 w-[30px] max-w-[30px]">
                          <div className="flex items-center justify-center">
                            {user?.role === "Admin" ? (
                              // Admin görünümü - dropdown ile teknisyen seçimi
                              production.assignedUserId ? (
                                <Select
                                  value={production.assignedUserId}
                                  onValueChange={(newUserId) => {
                                    if (newUserId === "none") {
                                      unassignTask(production.id);
                                    } else {
                                      reassignTask(production.id, newUserId);
                                    }
                                  }}
                                >
                                  <SelectTrigger className="w-full h-8 text-xs">
                                    <SelectValue>
                                      {(() => {
                                        const tech = technicians.find(
                                          (t) =>
                                            t.id === production.assignedUserId
                                        );
                                        return tech ? (
                                          <div className="flex items-center gap-2 w-full">
                                            <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-700 font-bold text-xs">
                                              {getInitials(tech.fullName)}
                                            </span>
                                            <span className="truncate text-xs">
                                              {tech.fullName}
                                            </span>
                                          </div>
                                        ) : (
                                          "Atanmış"
                                        );
                                      })()}
                                    </SelectValue>
                                  </SelectTrigger>
                                  <SelectContent>
                                    {technicians.map((tech) => (
                                      <SelectItem key={tech.id} value={tech.id}>
                                        <div className="flex items-center gap-2">
                                          <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                            {getInitials(tech.fullName)}
                                          </span>
                                          {tech.fullName}
                                        </div>
                                      </SelectItem>
                                    ))}
                                    <SelectItem value="none">
                                      Atamayı Kaldır
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : (
                                <Select
                                  onValueChange={(userId) => {
                                    if (userId) {
                                      reassignTask(production.id, userId);
                                    }
                                  }}
                                >
                                  <SelectTrigger className="w-full h-8 text-xs">
                                    <SelectValue placeholder="Teknisyen Seç" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {technicians.map((tech) => (
                                      <SelectItem key={tech.id} value={tech.id}>
                                        <div className="flex items-center gap-2">
                                          <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                            {getInitials(tech.fullName)}
                                          </span>
                                          {tech.fullName}
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              )
                            ) : user?.role === "Technician" ? (
                              // Teknisyen görünümü
                              production.assignedUserId === user?.id ? (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          unassignTask(production.id)
                                        }
                                        className="h-8 w-8 p-0"
                                      >
                                        <UserX className="h-4 w-4 text-red-500" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Görevi Bırak</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              ) : !production.assignedUserId ? (
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleTaskAssign(production.id)
                                        }
                                        className="h-8 w-8 p-0"
                                      >
                                        <UserCheck className="h-4 w-4 text-green-500" />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Görevi Al</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              ) : (
                                <span className="text-xs text-gray-500 flex items-center gap-1">
                                  <span className="inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 text-gray-700 font-bold text-xs">
                                    {getInitials(
                                      technicians.find(
                                        (t) =>
                                          t.id === production.assignedUserId
                                      )?.fullName || ""
                                    )}
                                  </span>
                                  {technicians.find(
                                    (t) => t.id === production.assignedUserId
                                  )?.fullName || "Atanmış"}
                                </span>
                              )
                            ) : // Diğer roller için görünüm
                            production.assignedUserId ? (
                              <span className="text-xs text-green-600 flex items-center gap-1">
                                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 font-bold">
                                  {getInitials(
                                    technicians.find(
                                      (t) => t.id === production.assignedUserId
                                    )?.fullName || ""
                                  )}
                                </span>
                                {technicians.find(
                                  (t) => t.id === production.assignedUserId
                                )?.fullName || "Atanmış"}
                              </span>
                            ) : (
                              <span className="text-xs text-gray-500">
                                Atanmamış
                              </span>
                            )}
                          </div>
                        </td>

                        {/* Açıklama */}
                        <td className="py-3 px-2 w-[40px] max-w-[40px] ">
                          <div
                            className={`flex items-center gap-2 ${
                              user?.role === "Admin" ? "cursor-pointer" : ""
                            }`}
                            onClick={
                              user?.role === "Admin"
                                ? () =>
                                    handleNotesEdit(
                                      production.id,
                                      production.notes || ""
                                    )
                                : undefined
                            }
                          >
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <span className="text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden ">
                                    {production.notes || "-"}
                                  </span>
                                </TooltipTrigger>
                                {production.notes && (
                                  <TooltipContent className="max-w-xs">
                                    <p>{production.notes}</p>
                                  </TooltipContent>
                                )}
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        </td>

                        {/* İşlemler */}
                        <td className="py-3 px-2 w-[30px] max-w-[30px] ">
                          {user?.role === "Admin" && (
                            <div className="flex gap-1 justify-center  w-[50px]">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleArchive(production.id)}
                                className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
                              >
                                <Archive className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDelete(production.id)}
                                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Name Edit Modal */}
      {editingNameId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Ad Soyad Düzenle</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ad
                </label>
                <input
                  type="text"
                  value={nameModalData.name}
                  onChange={(e) =>
                    setNameModalData({ ...nameModalData, name: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Ad girin..."
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Soyad
                </label>
                <input
                  type="text"
                  value={nameModalData.surname}
                  onChange={(e) =>
                    setNameModalData({
                      ...nameModalData,
                      surname: e.target.value,
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Soyad girin..."
                />
              </div>
            </div>
            <div className="flex gap-2 mt-6">
              <Button onClick={handleNameSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleNameCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Date Edit Modal */}
      {editingDateId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Onay Tarihi Düzenle</h3>
            <input
              type="date"
              value={tempDateValue}
              onChange={(e) => setTempDateValue(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="flex gap-2 mt-4">
              <Button onClick={handleDateSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleDateCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Target Date Edit Modal */}
      {editingTargetDateId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">
              Teslimat Tarihi Düzenle
            </h3>
            <input
              type="date"
              value={tempTargetDateValue}
              onChange={(e) => setTempTargetDateValue(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <div className="flex gap-2 mt-4">
              <Button onClick={handleTargetDateSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleTargetDateCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Target Days Edit Modal */}
      {editingTargetDaysId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Hedef Gün Düzenle</h3>
            <div className="space-y-2 mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Kaç gün sonra teslim edilecek?
              </label>
              <input
                type="number"
                value={tempTargetDaysValue}
                onChange={(e) => setTempTargetDaysValue(e.target.value)}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Gün sayısını girin..."
              />
              <p className="text-sm text-gray-500">
                Onay tarihinden itibaren bu kadar gün sonra teslimat tarihi
                otomatik hesaplanacak.
              </p>
            </div>
            <div className="flex gap-2 mt-4">
              <Button onClick={handleTargetDaysSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleTargetDaysCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Replication Edit Modal */}
      {editingReplicationId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">
              Çoğaltma Sayısı Düzenle
            </h3>
            <input
              type="number"
              value={tempReplicationValue}
              onChange={(e) => setTempReplicationValue(e.target.value)}
              min="0"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Çoğaltma sayısını girin..."
            />
            <div className="flex gap-2 mt-4">
              <Button onClick={handleReplicationSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleReplicationCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notes Edit Modal */}
      {editingNotesId && (
        <div className="fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Açıklama Düzenle</h3>
            <textarea
              value={notesModalData}
              onChange={(e) => setNotesModalData(e.target.value)}
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              placeholder="Açıklama girin..."
            />
            <div className="flex gap-2 mt-4">
              <Button onClick={handleNotesSave} className="flex-1">
                <Save className="h-4 w-4 mr-2" />
                Kaydet
              </Button>
              <Button
                variant="outline"
                onClick={handleNotesCancel}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                İptal
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      <Dialog open={deleteConfirmId !== null} onOpenChange={cancelDelete}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Üretimi Sil</DialogTitle>
            <DialogDescription>
              Bu üretimi silmek istediğinizden emin misiniz? Bu işlem geri
              alınamaz.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelDelete}>
              İptal
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Sil
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Archive Confirmation Modal */}
      <Dialog open={archiveConfirmId !== null} onOpenChange={cancelArchive}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Üretimi Arşivle</DialogTitle>
            <DialogDescription>
              Bu üretimi arşive almak istediğinizden emin misiniz? Arşivlenen
              üretimler plan arşiv bölümünde görüntülenebilir.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelArchive}>
              İptal
            </Button>
            <Button onClick={confirmArchive}>Arşivle</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Task Assignment Confirmation Modal */}
      <Dialog open={assignConfirmId !== null} onOpenChange={cancelTaskAssign}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Görev Alma Onayı</DialogTitle>
            <DialogDescription>
              Bu görevi almak istediğinizden emin misiniz? Görev size atanacak
              ve süreç takibi kısmını sadece siz düzenleyebileceksiniz.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={cancelTaskAssign}>
              İptal
            </Button>
            <Button onClick={confirmTaskAssign}>Görevi Al</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
