"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/bitenler/page",{

/***/ "(app-pages-browser)/./src/app/tasks/bitenler/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/tasks/bitenler/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BitenlerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nfunction BitenlerPage() {\n    _s();\n    const [archivedProductions, setArchivedProductions] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [unarchiveConfirmId, setUnarchiveConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const { user } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore)();\n    const fetchArchivedProductions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/archived\"));\n            if (response.ok) {\n                const data = await response.json();\n                setArchivedProductions(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching archived productions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const unarchiveProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id, \"/unarchive\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setArchivedProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error unarchiving production:\", error);\n        }\n        return false;\n    };\n    const handleUnarchive = (id)=>{\n        setUnarchiveConfirmId(id);\n    };\n    const confirmUnarchive = async ()=>{\n        if (unarchiveConfirmId) {\n            const success = await unarchiveProduction(unarchiveConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Üretim başarıyla arşivden çıkarıldı!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Üretim arşivden çıkarılamadı!\");\n            }\n            setUnarchiveConfirmId(null);\n        }\n    };\n    const cancelUnarchive = ()=>{\n        setUnarchiveConfirmId(null);\n    };\n    const fetchUsers = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.apiService.getAllUsers();\n            setUsers(data);\n        } catch (error) {\n            console.error(\"Error fetching users:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"BitenlerPage.useEffect\": ()=>{\n            fetchArchivedProductions();\n            fetchUsers();\n        }\n    }[\"BitenlerPage.useEffect\"], []);\n    const filteredProductions = archivedProductions.filter((p)=>p.name.toLowerCase().includes(searchTerm.toLowerCase()) || p.surname.toLowerCase().includes(searchTerm.toLowerCase()) || p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()));\n    const calculateTargetDays = (targetDate, approvalDate)=>{\n        if (!targetDate || !approvalDate) return null;\n        const target = new Date(targetDate);\n        const approval = new Date(approvalDate);\n        const diffTime = target.getTime() - approval.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const calculateRemainingDays = (targetDate)=>{\n        if (!targetDate) return null;\n        const target = new Date(targetDate);\n        const today = new Date();\n        const diffTime = target.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"tr-TR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    // Son Yapan'ı bulmak için yardımcı fonksiyon\n    function getUserName(userId) {\n        if (!userId) return \"-\";\n        const u = users.find((u)=>u.id === userId);\n        return u ? u.fullName : userId;\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Y\\xfckleniyor...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 w-full mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Arşivlenmiş \\xdcretimler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Arşive alınmış \\xfcretimleri g\\xf6r\\xfcnt\\xfcleyin ve y\\xf6netin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            \"Rapor İndir\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"h-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Toplam Arşiv\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: archivedProductions.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"w-full h-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Hasta adı, soyadı veya notlarda ara...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            children: \"Arşivlenmiş \\xdcretimler\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]\",\n                                                            children: \"Ad Soyad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] \",\n                                                            children: \"Onay Kutuları\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]\",\n                                                            children: \"Onay Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"Hedef G\\xfcn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]\",\n                                                            children: \"Teslimat Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900   w-[40px] max-w-[40px]\",\n                                                            children: \"Tamamlanma Durumu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"cursor-pointer\",\n                                                                                children: \"\\xc7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 278,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: \"\\xc7oğaltma\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] \",\n                                                            children: \"S\\xfcre\\xe7 Takibi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"A\\xe7ıklama\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"Son Yapan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"İşlemler\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredProductions.map((production, index)=>{\n                                                    const remainingDays = calculateRemainingDays(production.targetDate);\n                                                    const targetDaysCount = calculateTargetDays(production.targetDate, production.approvalDate);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50 bg-gray-50/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1 text-center border !w-[1px] !max-w-[1px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-700 !text-[13px] \",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1  !w-[50px] !max-w-[50px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 cursor-pointer block truncate\",\n                                                                                    children: (()=>{\n                                                                                        const fullName = \"\".concat(production.name, \" \").concat(production.surname);\n                                                                                        return fullName.length > 15 ? \"\".concat(fullName.substring(0, 15), \"...\") : fullName;\n                                                                                    })()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 326,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: [\n                                                                                                production.name,\n                                                                                                \" \",\n                                                                                                production.surname\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 338,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"mt-1 text-sm \",\n                                                                                            children: production.notes\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 342,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 337,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  !w-[50px] !max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-3 justify-center  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center \",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.pStar,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-blue-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 359,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 358,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 357,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"P*\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 368,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 367,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 356,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.greening,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 376,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 375,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 374,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Yeşil\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 385,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 384,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 373,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.adminApproval,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 393,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 392,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 391,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Onay\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 402,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 401,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 390,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[30px] max-w-[30px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.approvalDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: targetDaysCount !== null ? \"\".concat(targetDaysCount, \" g\\xfcn\") : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.targetDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[30px] max-w-[30px] \",\n                                                                children: remainingDays !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm px-2 py-1 rounded \".concat(remainingDays < 0 ? \"bg-red-100 text-red-800\" : remainingDays === 0 ? \"bg-green-100 text-green-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                    children: remainingDays < 0 ? \"\".concat(Math.abs(remainingDays), \" g\\xfcn ge\\xe7\") : remainingDays === 0 ? \"Zamanında\" : \"\".concat(remainingDays, \" g\\xfcn erken\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[5px] max-w-[5px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center  \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-center\",\n                                                                        children: production.replication || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 458,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[50px] max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2 justify-center flex-wrap  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.model,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 471,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 470,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 469,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Model\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 480,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 479,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.platePressing,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-yellow-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 488,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 487,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 486,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Plak Basma\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 497,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 496,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.fineCut,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-orange-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 505,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 504,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"İnce Kesim\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 513,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.packaging,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 522,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 521,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 520,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Paketleme\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 531,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 530,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.shipping,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-red-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 539,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 538,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 537,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Kargo\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 547,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden cursor-help\",\n                                                                                        children: production.notes || \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 561,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    className: \"max-w-xs\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: production.notes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 567,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 566,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: getUserName(production.assignedUserId)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[30px] max-w-[30px] \",\n                                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-1 justify-center  w-[50px]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleUnarchive(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-green-600 hover:text-green-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 584,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, production.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    filteredProductions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: searchTerm ? \"Arama kriterleriyle eşleşen arşivlenmiş üretim bulunamadı.\" : \"Henüz arşivlenmiş üretim bulunmamaktadır.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: unarchiveConfirmId !== null,\n                onOpenChange: cancelUnarchive,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi arşivden \\xe7ıkarmak istediğinizden emin misiniz? \\xdcretim aktif listesine geri d\\xf6nd\\xfcr\\xfclecek.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 615,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelUnarchive,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmUnarchive,\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 622,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                    lineNumber: 614,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 613,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n_s(BitenlerPage, \"5jgni1wt2lO1x4YpSgPr9zA44dw=\", false, function() {\n    return [\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore\n    ];\n});\n_c = BitenlerPage;\nvar _c;\n$RefreshReg$(_c, \"BitenlerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/bitenler/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   apiService: () => (/* binding */ apiService)\n/* harmony export */ });\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n\nconst API_BASE = \"http://localhost:5000/api\" || 0;\nclass ApiError extends Error {\n    constructor(status, message){\n        super(message), this.status = status;\n        this.name = \"ApiError\";\n    }\n}\nclass ApiService {\n    getHeaders() {\n        let includeAuth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (includeAuth) {\n            const token = _stores_authStore__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState().token;\n            if (token) {\n                headers[\"Authorization\"] = \"Bearer \".concat(token);\n            }\n        }\n        return headers;\n    }\n    async handleResponse(response) {\n        if (!response.ok) {\n            const errorText = await response.text();\n            throw new ApiError(response.status, errorText || \"Request failed\");\n        }\n        const contentType = response.headers.get(\"content-type\");\n        if (contentType && contentType.includes(\"application/json\")) {\n            return response.json();\n        }\n        return null;\n    }\n    async login(credentials) {\n        const response = await fetch(\"\".concat(API_BASE, \"/auth/login\"), {\n            method: \"POST\",\n            headers: this.getHeaders(),\n            body: JSON.stringify(credentials)\n        });\n        return this.handleResponse(response);\n    }\n    async register(userData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/auth/register\"), {\n            method: \"POST\",\n            headers: this.getHeaders(),\n            body: JSON.stringify(userData)\n        });\n        return this.handleResponse(response);\n    }\n    async getCurrentUser() {\n        const response = await fetch(\"\".concat(API_BASE, \"/auth/me\"), {\n            method: \"GET\",\n            headers: this.getHeaders(true)\n        });\n        return this.handleResponse(response);\n    }\n    async getAllUsers() {\n        const response = await fetch(\"\".concat(API_BASE, \"/user\"), {\n            method: \"GET\",\n            headers: this.getHeaders(true)\n        });\n        return this.handleResponse(response);\n    }\n    async updateUser(userId, userData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/\").concat(userId), {\n            method: \"PUT\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify(userData)\n        });\n        return this.handleResponse(response);\n    }\n    async updateUserRole(userId, roleData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/\").concat(userId, \"/role\"), {\n            method: \"PUT\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify(roleData)\n        });\n        return this.handleResponse(response);\n    }\n    async deleteUser(userId) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/\").concat(userId), {\n            method: \"DELETE\",\n            headers: this.getHeaders(true)\n        });\n        return this.handleResponse(response);\n    }\n    async toggleUserStatus(userId, isActive) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/\").concat(userId, \"/status\"), {\n            method: \"PUT\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify({\n                isActive\n            })\n        });\n        return this.handleResponse(response);\n    }\n    async createUser(userData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user\"), {\n            method: \"POST\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify(userData)\n        });\n        return this.handleResponse(response);\n    }\n    async getAllRoles() {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/roles\"), {\n            method: \"GET\",\n            headers: this.getHeaders(true)\n        });\n        return this.handleResponse(response);\n    }\n    async createRole(roleData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/roles\"), {\n            method: \"POST\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify(roleData)\n        });\n        return this.handleResponse(response);\n    }\n    async deleteRole(roleName) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/roles/\").concat(encodeURIComponent(roleName)), {\n            method: \"DELETE\",\n            headers: this.getHeaders(true)\n        });\n        return this.handleResponse(response);\n    }\n    async changePassword(passwordData) {\n        const response = await fetch(\"\".concat(API_BASE, \"/user/change-password\"), {\n            method: \"POST\",\n            headers: this.getHeaders(true),\n            body: JSON.stringify(passwordData)\n        });\n        return this.handleResponse(response);\n    }\n}\nconst apiService = new ApiService();\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});