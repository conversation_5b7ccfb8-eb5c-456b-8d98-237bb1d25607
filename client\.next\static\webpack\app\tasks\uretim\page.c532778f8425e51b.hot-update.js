"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/uretim/page",{

/***/ "(app-pages-browser)/./src/app/tasks/uretim/page.tsx":
/*!***************************************!*\
  !*** ./src/app/tasks/uretim/page.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UretimPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,Calendar,Check,Clock,Package,Pause,Play,RefreshCw,Save,Search,Trash2,Truck,UserCheck,UserX,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_add_vaka_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./_components/add-vaka-modal */ \"(app-pages-browser)/./src/app/tasks/uretim/_components/add-vaka-modal.tsx\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useSignalR */ \"(app-pages-browser)/./src/hooks/useSignalR.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Ad ve soyad baş harflerini döndüren yardımcı fonksiyon\nfunction getInitials(fullName) {\n    if (!fullName) return \"\";\n    const [name, surname] = fullName.split(\" \");\n    return ((name === null || name === void 0 ? void 0 : name[0]) || \"\").toUpperCase() + ((surname === null || surname === void 0 ? void 0 : surname[0]) || \"\").toUpperCase();\n}\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nfunction UretimPage() {\n    _s();\n    const [productions, setProductions] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]);\n    const [technicians, setTechnicians] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        total: 0,\n        approved: 0,\n        inProgress: 0,\n        shipped: 0\n    });\n    const [editingNameId, setEditingNameId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingDateId, setEditingDateId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingTargetDateId, setEditingTargetDateId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingTargetDaysId, setEditingTargetDaysId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingReplicationId, setEditingReplicationId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [editingNotesId, setEditingNotesId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [deleteConfirmId, setDeleteConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [archiveConfirmId, setArchiveConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [assignConfirmId, setAssignConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(null);\n    const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(10);\n    const [lastUpdated, setLastUpdated] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(new Date());\n    const [nameModalData, setNameModalData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        name: \"\",\n        surname: \"\"\n    });\n    const [notesModalData, setNotesModalData] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempDateValue, setTempDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempTargetDateValue, setTempTargetDateValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempTargetDaysValue, setTempTargetDaysValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const [tempReplicationValue, setTempReplicationValue] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(\"\");\n    const { user } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_9__.useAuthStore)();\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalR)();\n    // Fetch technicians\n    const fetchTechnicians = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchTechnicians]\": async ()=>{\n            try {\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/User/technicians\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setTechnicians(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching technicians:\", error);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchTechnicians]\"], [\n        API_BASE_URL\n    ]);\n    // Task assignment functions\n    const assignTask = async (productionId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(productionId, \"/assign\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(user === null || user === void 0 ? void 0 : user.id)\n            });\n            if (response.ok) {\n                const updatedProduction = await response.json();\n                setProductions((prev)=>prev.map((p)=>p.id === productionId ? updatedProduction : p));\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Görev başarıyla alındı!\");\n            } else {\n                const errorData = await response.text();\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(errorData || \"Görev alınamadı!\");\n            }\n        } catch (error) {\n            console.error(\"Error assigning task:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Görev alınamadı!\");\n        }\n    };\n    const unassignTask = async (productionId)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(productionId, \"/unassign\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                const updatedProduction = await response.json();\n                setProductions((prev)=>prev.map((p)=>p.id === productionId ? updatedProduction : p));\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Görev başarıyla bırakıldı!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Görev bırakılamadı!\");\n            }\n        } catch (error) {\n            console.error(\"Error unassigning task:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Görev bırakılamadı!\");\n        }\n    };\n    const reassignTask = async (productionId, newUserId)=>{\n        try {\n            // First unassign, then assign to new user\n            await unassignTask(productionId);\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(productionId, \"/assign\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(newUserId)\n            });\n            if (response.ok) {\n                const updatedProduction = await response.json();\n                setProductions((prev)=>prev.map((p)=>p.id === productionId ? updatedProduction : p));\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Görev başarıyla yeniden atandı!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Görev yeniden atanamadı!\");\n            }\n        } catch (error) {\n            console.error(\"Error reassigning task:\", error);\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Görev yeniden atanamadı!\");\n        }\n    };\n    const handleTaskAssign = (id)=>{\n        setAssignConfirmId(id);\n    };\n    const confirmTaskAssign = async ()=>{\n        if (assignConfirmId && (user === null || user === void 0 ? void 0 : user.id)) {\n            await assignTask(assignConfirmId);\n            setAssignConfirmId(null);\n        }\n    };\n    const cancelTaskAssign = ()=>{\n        setAssignConfirmId(null);\n    };\n    const fetchProductions = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchProductions]\": async ()=>{\n            try {\n                setLoading(true);\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/Production\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setProductions(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching productions:\", error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchProductions]\"], [\n        API_BASE_URL\n    ]);\n    const fetchStats = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[fetchStats]\": async ()=>{\n            try {\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/stats\"));\n                if (response.ok) {\n                    const data = await response.json();\n                    setStats(data);\n                }\n            } catch (error) {\n                console.error(\"Error fetching stats:\", error);\n            }\n        }\n    }[\"UretimPage.useCallback[fetchStats]\"], [\n        API_BASE_URL\n    ]);\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)({\n        \"UretimPage.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                fetchProductions(),\n                fetchStats()\n            ]);\n            setLastUpdated(new Date());\n            setCountdown(10);\n        }\n    }[\"UretimPage.useCallback[refreshData]\"], [\n        fetchProductions,\n        fetchStats\n    ]);\n    const formatLastUpdated = (date)=>{\n        return date.toLocaleTimeString(\"tr-TR\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            second: \"2-digit\"\n        });\n    };\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionCreated\", {\n        \"UretimPage.useSignalREvent\": (production)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>[\n                        ...prev,\n                        production\n                    ]\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Yeni \\xfcretim eklendi: \".concat(production.name, \" \").concat(production.surname));\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionUpdated\", {\n        \"UretimPage.useSignalREvent\": (production)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>prev.map({\n                        \"UretimPage.useSignalREvent\": (p)=>p.id === production.id ? production : p\n                    }[\"UretimPage.useSignalREvent\"])\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"\\xdcretim g\\xfcncellendi: \".concat(production.name, \" \").concat(production.surname));\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    (0,_hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent)(\"ProductionDeleted\", {\n        \"UretimPage.useSignalREvent\": (productionId)=>{\n            setProductions({\n                \"UretimPage.useSignalREvent\": (prev)=>prev.filter({\n                        \"UretimPage.useSignalREvent\": (p)=>p.id !== productionId\n                    }[\"UretimPage.useSignalREvent\"])\n            }[\"UretimPage.useSignalREvent\"]);\n            fetchStats();\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Üretim silindi\");\n        }\n    }[\"UretimPage.useSignalREvent\"]);\n    const updateProduction = async (id, updates)=>{\n        try {\n            const production = productions.find((p)=>p.id === id);\n            if (!production) return false;\n            const updatedProduction = {\n                ...production,\n                ...updates\n            };\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(updatedProduction)\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.map((p)=>p.id === id ? updatedProduction : p));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error updating production:\", error);\n        }\n        return false;\n    };\n    const deleteProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id), {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error deleting production:\", error);\n        }\n        return false;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            fetchProductions();\n            fetchStats();\n            fetchTechnicians();\n        }\n    }[\"UretimPage.useEffect\"], [\n        fetchProductions,\n        fetchStats,\n        fetchTechnicians\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            fetchStats();\n        }\n    }[\"UretimPage.useEffect\"], [\n        productions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            let intervalId;\n            if (isAutoRefreshEnabled && countdown > 0) {\n                intervalId = setInterval({\n                    \"UretimPage.useEffect\": ()=>{\n                        setCountdown({\n                            \"UretimPage.useEffect\": (prev)=>prev - 1\n                        }[\"UretimPage.useEffect\"]);\n                    }\n                }[\"UretimPage.useEffect\"], 1000);\n            } else if (isAutoRefreshEnabled && countdown === 0) {\n                refreshData();\n            }\n            return ({\n                \"UretimPage.useEffect\": ()=>{\n                    if (intervalId) clearInterval(intervalId);\n                }\n            })[\"UretimPage.useEffect\"];\n        }\n    }[\"UretimPage.useEffect\"], [\n        isAutoRefreshEnabled,\n        countdown,\n        refreshData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)({\n        \"UretimPage.useEffect\": ()=>{\n            if (isAutoRefreshEnabled) {\n                setCountdown(10);\n            }\n        }\n    }[\"UretimPage.useEffect\"], [\n        isAutoRefreshEnabled\n    ]);\n    const calculateTargetDays = (targetDate, approvalDate)=>{\n        if (!targetDate || !approvalDate) return null;\n        const target = new Date(targetDate);\n        const approval = new Date(approvalDate);\n        const diffTime = target.getTime() - approval.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const calculateRemainingDays = (targetDate)=>{\n        if (!targetDate) return null;\n        const target = new Date(targetDate);\n        const today = new Date();\n        const diffTime = target.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"tr-TR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    const handleDelete = (id)=>{\n        setDeleteConfirmId(id);\n    };\n    const confirmDelete = async ()=>{\n        if (deleteConfirmId) {\n            const success = await deleteProduction(deleteConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Üretim başarıyla silindi!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim silinemedi!\");\n            }\n            setDeleteConfirmId(null);\n        }\n    };\n    const cancelDelete = ()=>{\n        setDeleteConfirmId(null);\n    };\n    const archiveProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id, \"/archive\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error archiving production:\", error);\n        }\n        return false;\n    };\n    const handleArchive = (id)=>{\n        setArchiveConfirmId(id);\n    };\n    const confirmArchive = async ()=>{\n        if (archiveConfirmId) {\n            const success = await archiveProduction(archiveConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Üretim başarıyla arşivlendi!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim arşivlenemedi!\");\n            }\n            setArchiveConfirmId(null);\n        }\n    };\n    const cancelArchive = ()=>{\n        setArchiveConfirmId(null);\n    };\n    const handleVakaAdded = ()=>{\n        fetchProductions();\n        fetchStats();\n        sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Yeni vaka başarıyla eklendi!\");\n    };\n    const handleCheckboxChange = async (id, field, value)=>{\n        const success = await updateProduction(id, {\n            [field]: value\n        });\n        if (success) {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Değişiklik başarıyla kaydedildi!\");\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Değişiklik kaydedilemedi!\");\n        }\n    };\n    const handleNameEdit = (id, name, surname)=>{\n        setEditingNameId(id);\n        setNameModalData({\n            name,\n            surname\n        });\n    };\n    const handleNameSave = async ()=>{\n        if (editingNameId) {\n            const success = await updateProduction(editingNameId, nameModalData);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Ad soyad başarıyla güncellendi!\");\n                setEditingNameId(null);\n                setNameModalData({\n                    name: \"\",\n                    surname: \"\"\n                });\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Ad soyad güncellenemedi!\");\n            }\n        }\n    };\n    const handleNameCancel = ()=>{\n        setEditingNameId(null);\n        setNameModalData({\n            name: \"\",\n            surname: \"\"\n        });\n    };\n    const handleDateEdit = (id, currentDate)=>{\n        setEditingDateId(id);\n        setTempDateValue(currentDate || \"\");\n    };\n    const handleDateSave = async ()=>{\n        if (editingDateId) {\n            const success = await updateProduction(editingDateId, {\n                approvalDate: tempDateValue\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Onay tarihi başarıyla güncellendi!\");\n                setEditingDateId(null);\n                setTempDateValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Onay tarihi güncellenemedi!\");\n            }\n        }\n    };\n    const handleDateCancel = ()=>{\n        setEditingDateId(null);\n        setTempDateValue(\"\");\n    };\n    const handleTargetDateEdit = (id, currentDate)=>{\n        setEditingTargetDateId(id);\n        setTempTargetDateValue(currentDate || \"\");\n    };\n    const handleTargetDateSave = async ()=>{\n        if (editingTargetDateId) {\n            const success = await updateProduction(editingTargetDateId, {\n                targetDate: tempTargetDateValue\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Teslimat tarihi başarıyla güncellendi!\");\n                setEditingTargetDateId(null);\n                setTempTargetDateValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Teslimat tarihi güncellenemedi!\");\n            }\n        }\n    };\n    const handleTargetDateCancel = ()=>{\n        setEditingTargetDateId(null);\n        setTempTargetDateValue(\"\");\n    };\n    const handleTargetDaysEdit = (id, currentDays)=>{\n        setEditingTargetDaysId(id);\n        setTempTargetDaysValue(currentDays.toString());\n    };\n    const handleTargetDaysSave = async ()=>{\n        if (editingTargetDaysId) {\n            const days = parseInt(tempTargetDaysValue) || 0;\n            const production = productions.find((p)=>p.id === editingTargetDaysId);\n            if (production) {\n                const startDate = production.approvalDate ? new Date(production.approvalDate) : new Date();\n                const targetDate = new Date(startDate);\n                targetDate.setDate(targetDate.getDate() + days);\n                const targetDateString = targetDate.toISOString().split(\"T\")[0];\n                const success = await updateProduction(editingTargetDaysId, {\n                    targetDate: targetDateString,\n                    ...production.approvalDate ? {} : {\n                        approvalDate: new Date().toISOString().split(\"T\")[0]\n                    }\n                });\n                if (success) {\n                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Hedef gün başarıyla güncellendi!\");\n                    setEditingTargetDaysId(null);\n                    setTempTargetDaysValue(\"\");\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Hedef gün güncellenemedi!\");\n                }\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Üretim bulunamadı!\");\n            }\n        }\n    };\n    const handleTargetDaysCancel = ()=>{\n        setEditingTargetDaysId(null);\n        setTempTargetDaysValue(\"\");\n    };\n    const handleReplicationEdit = (id, currentValue)=>{\n        setEditingReplicationId(id);\n        setTempReplicationValue(currentValue.toString());\n    };\n    const handleReplicationSave = async ()=>{\n        if (editingReplicationId) {\n            const success = await updateProduction(editingReplicationId, {\n                replication: parseInt(tempReplicationValue) || 0\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Çoğaltma sayısı başarıyla güncellendi!\");\n                setEditingReplicationId(null);\n                setTempReplicationValue(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Çoğaltma sayısı güncellenemedi!\");\n            }\n        }\n    };\n    const handleReplicationCancel = ()=>{\n        setEditingReplicationId(null);\n        setTempReplicationValue(\"\");\n    };\n    const handleNotesEdit = (id, currentNotes)=>{\n        setEditingNotesId(id);\n        setNotesModalData(currentNotes || \"\");\n    };\n    const handleNotesSave = async ()=>{\n        if (editingNotesId) {\n            const success = await updateProduction(editingNotesId, {\n                notes: notesModalData\n            });\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Açıklama başarıyla güncellendi!\");\n                setEditingNotesId(null);\n                setNotesModalData(\"\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Açıklama güncellenemedi!\");\n            }\n        }\n    };\n    const handleNotesCancel = ()=>{\n        setEditingNotesId(null);\n        setNotesModalData(\"\");\n    };\n    const filteredProductions = productions.filter((p)=>p.name.toLowerCase().includes(searchTerm.toLowerCase()) || p.surname.toLowerCase().includes(searchTerm.toLowerCase()) || p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b)=>{\n        const remainingA = calculateRemainingDays(a.targetDate);\n        const remainingB = calculateRemainingDays(b.targetDate);\n        if (remainingA === null && remainingB === null) return 0;\n        if (remainingA === null) return 1;\n        if (remainingB === null) return -1;\n        return remainingA - remainingB;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Y\\xfckleniyor...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 678,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n            lineNumber: 677,\n            columnNumber: 7\n        }, this);\n    }\n    const toggleAutoRefresh = ()=>{\n        setIsAutoRefreshEnabled((prev)=>{\n            if (!prev) setCountdown(10);\n            return !prev;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 w-full mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"\\xdcretim Y\\xf6netimi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"\\xdcretim s\\xfcre\\xe7lerini takip edin ve y\\xf6netin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"border-0 shadow-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: toggleAutoRefresh,\n                                                        className: \"h-8 w-8 p-0\",\n                                                        children: isAutoRefreshEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 707,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: refreshData,\n                                                        className: \"h-8 w-8 p-0\",\n                                                        disabled: loading,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4 \".concat(loading ? \"animate-spin\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 720,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 706,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-xs text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            isAutoRefreshEnabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Yenileme: \",\n                                                                    countdown,\n                                                                    \"s\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Duraklatıldı\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 739,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 734,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Son: \",\n                                                            formatLastUpdated(lastUpdated)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 17\n                                            }, this),\n                                            isAutoRefreshEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-1 bg-gray-200 rounded-full overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full bg-blue-500 transition-all duration-1000 ease-linear\",\n                                                    style: {\n                                                        width: \"\".concat((10 - countdown) / 10 * 100, \"%\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                    lineNumber: 750,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 703,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_add_vaka_modal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                onSuccess: handleVakaAdded,\n                                apiBaseUrl: API_BASE_URL\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 760,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 701,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Toplam\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.total\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 767,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 766,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Onaylı\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.approved\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 781,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 780,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"Devam Eden\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.inProgress\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-8 w-8 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 796,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 794,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: \"G\\xf6nderilen\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stats.shipped\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 813,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 809,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 808,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 765,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 828,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Hasta adı, soyadı veya notlarda ara...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 827,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 825,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 824,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            children: \"\\xdcretim Listesi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 844,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 843,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full \",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"border-b\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] \"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]\",\n                                                        children: \"Ad Soyad\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] \",\n                                                        children: \"Onay Kutuları\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]\",\n                                                        children: \"Onay Tarihi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"Hedef G\\xfcn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]\",\n                                                        children: \"Teslimat Tarihi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 865,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900   w-[20px] max-w-[20px]\",\n                                                        children: \"Kalan G\\xfcn\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 868,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] \",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                        asChild: true,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"cursor-pointer\",\n                                                                            children: \"\\xc7\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 874,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"\\xc7oğaltma\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 878,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 877,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] \",\n                                                        children: \"S\\xfcre\\xe7 Takibi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 883,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"G\\xf6rev Atama\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 886,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                        children: \"A\\xe7ıklama\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                        children: \"İşlemler\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 850,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: filteredProductions.map((production, index)=>{\n                                                var _technicians_find, _technicians_find1, _technicians_find2, _technicians_find3;\n                                                const remainingDays = calculateRemainingDays(production.targetDate);\n                                                const targetDaysCount = calculateTargetDays(production.targetDate, production.approvalDate);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b hover:bg-gray-50 \".concat(remainingDays !== null ? remainingDays < 0 ? \"border-l-4 border-l-red-700 bg-red-50\" : remainingDays <= 2 && remainingDays >= 0 ? \"animate-pulse border-l-4 border-l-red-500 bg-red-50\" : \"\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-1 text-center border !w-[1px] !max-w-[1px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-700 !text-[13px] \",\n                                                                children: index + 1\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-1  !w-[50px] !max-w-[50px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleNameEdit(production.id, production.name, production.surname) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-900  block truncate\",\n                                                                                    children: (()=>{\n                                                                                        const fullName = \"\".concat(production.name, \" \").concat(production.surname);\n                                                                                        return fullName.length > 15 ? \"\".concat(fullName.substring(0, 15), \"...\") : fullName;\n                                                                                    })()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 947,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 946,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: [\n                                                                                                production.name,\n                                                                                                \" \",\n                                                                                                production.surname\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 958,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"mt-1 text-sm \",\n                                                                                            children: production.notes\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 962,\n                                                                                            columnNumber: 39\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 957,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 956,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 945,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 944,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 929,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 928,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  !w-[50px] !max-w-[50px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-3 justify-center  \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center \",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.pStar,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"pStar\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-blue-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 980,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 979,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 978,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"P*\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 999,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 998,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 977,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 976,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.greening,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"greening\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-green-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1007,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1006,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1005,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Yeşil\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1026,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1025,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1004,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1003,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.adminApproval,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"adminApproval\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-purple-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && (user === null || user === void 0 ? void 0 : user.role) !== \"Software\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1034,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1033,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1032,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Onay\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1053,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1052,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1031,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1030,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 975,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[30px] max-w-[30px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 relative \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleDateEdit(production.id, production.approvalDate || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.approvalDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1062,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1061,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[20px] max-w-[20px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleTargetDaysEdit(production.id, targetDaysCount || 0) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: targetDaysCount !== null ? \"\".concat(targetDaysCount, \" g\\xfcn\") : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1098,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1084,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[20px] max-w-[20px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleTargetDateEdit(production.id, production.targetDate || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.targetDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1122,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1108,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[30px] max-w-[30px] \",\n                                                            children: remainingDays !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm px-2 py-1 rounded \".concat(remainingDays < 0 ? \"bg-red-600 text-white\" : remainingDays <= 2 && remainingDays >= 0 ? \"bg-red-500 text-white animate-pulse\" : \"bg-green-100 text-green-800\"),\n                                                                children: remainingDays > 0 ? \"\".concat(remainingDays, \" g\\xfcn\") : remainingDays === 0 ? \"Bugün\" : \"-\".concat(Math.abs(remainingDays), \" g\\xfcn\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 29\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1147,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1129,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2   w-[5px] max-w-[5px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 justify-center \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" || (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" && production.assignedUserId === (user === null || user === void 0 ? void 0 : user.id) ? ()=>handleReplicationEdit(production.id, production.replication || 0) : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-center\",\n                                                                    children: production.replication || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1172,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1153,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1152,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2  w-[50px] max-w-[50px]  \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-2 justify-center flex-wrap  \",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.model,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"model\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-green-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1185,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1184,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1183,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Model\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1206,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1205,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1182,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1181,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.platePressing,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"platePressing\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-yellow-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1214,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1213,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1212,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Plak Basma\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1235,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1234,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1211,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1210,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.fineCut,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"fineCut\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-orange-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1243,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1242,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1241,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"İnce Kesim\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1264,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1263,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1240,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1239,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.packaging,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"packaging\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-purple-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1272,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1271,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1270,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Paketleme\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1293,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1292,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1269,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1268,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            type: \"checkbox\",\n                                                                                            checked: production.shipping,\n                                                                                            onChange: (e)=>handleCheckboxChange(production.id, \"shipping\", e.target.checked),\n                                                                                            className: \"w-4 h-4 text-red-600\",\n                                                                                            disabled: (user === null || user === void 0 ? void 0 : user.role) !== \"Admin\" && ((user === null || user === void 0 ? void 0 : user.role) !== \"Technician\" || production.assignedUserId !== (user === null || user === void 0 ? void 0 : user.id))\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1301,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1300,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1299,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: \"Kargo\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1322,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1321,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1298,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1297,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1180,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1179,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[30px] max-w-[30px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center\",\n                                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? // Admin görünümü - dropdown ile teknisyen seçimi\n                                                                production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    value: production.assignedUserId,\n                                                                    onValueChange: (newUserId)=>{\n                                                                        if (newUserId === \"none\") {\n                                                                            unassignTask(production.id);\n                                                                        } else {\n                                                                            reassignTask(production.id, newUserId);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                            className: \"w-full h-8 text-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                children: (()=>{\n                                                                                    const tech = technicians.find((t)=>t.id === production.assignedUserId);\n                                                                                    return tech ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2 w-full\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-green-100 text-green-700 font-bold text-xs\",\n                                                                                                children: getInitials(tech.fullName)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1354,\n                                                                                                columnNumber: 45\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"truncate text-xs\",\n                                                                                                children: tech.fullName\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1357,\n                                                                                                columnNumber: 45\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1353,\n                                                                                        columnNumber: 43\n                                                                                    }, this) : \"Atanmış\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1346,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1345,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                            children: [\n                                                                                technicians.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                        value: tech.id,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center gap-2\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                    className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                                                    children: getInitials(tech.fullName)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                    lineNumber: 1371,\n                                                                                                    columnNumber: 43\n                                                                                                }, this),\n                                                                                                tech.fullName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                            lineNumber: 1370,\n                                                                                            columnNumber: 41\n                                                                                        }, this)\n                                                                                    }, tech.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1369,\n                                                                                        columnNumber: 39\n                                                                                    }, this)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                    value: \"none\",\n                                                                                    children: \"Atamayı Kaldır\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1378,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1367,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1335,\n                                                                    columnNumber: 33\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                                                    onValueChange: (userId)=>{\n                                                                        if (userId) {\n                                                                            reassignTask(production.id, userId);\n                                                                        }\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                                            className: \"w-full h-8 text-xs\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                                                placeholder: \"Teknisyen Se\\xe7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1392,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1391,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                                            children: technicians.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                                                    value: tech.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                                                children: getInitials(tech.fullName)\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                                lineNumber: 1398,\n                                                                                                columnNumber: 43\n                                                                                            }, this),\n                                                                                            tech.fullName\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1397,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, tech.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1396,\n                                                                                    columnNumber: 39\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1394,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1384,\n                                                                    columnNumber: 33\n                                                                }, this) : (user === null || user === void 0 ? void 0 : user.role) === \"Technician\" ? // Teknisyen görünümü\n                                                                production.assignedUserId === (user === null || user === void 0 ? void 0 : user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>unassignTask(production.id),\n                                                                                    className: \"h-8 w-8 p-0\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-red-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1422,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1414,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1413,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"G\\xf6revi Bırak\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1426,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1425,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1411,\n                                                                    columnNumber: 33\n                                                                }, this) : !production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                    variant: \"outline\",\n                                                                                    size: \"sm\",\n                                                                                    onClick: ()=>handleTaskAssign(production.id),\n                                                                                    className: \"h-8 w-8 p-0\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"h-4 w-4 text-green-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                        lineNumber: 1442,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1434,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1433,\n                                                                                columnNumber: 37\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: \"G\\xf6revi Al\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1446,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1445,\n                                                                                columnNumber: 37\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 35\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1431,\n                                                                    columnNumber: 33\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 text-gray-700 font-bold text-xs\",\n                                                                            children: getInitials(((_technicians_find = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find === void 0 ? void 0 : _technicians_find.fullName) || \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1452,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        ((_technicians_find1 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find1 === void 0 ? void 0 : _technicians_find1.fullName) || \"Atanmış\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1451,\n                                                                    columnNumber: 33\n                                                                }, this) : production.assignedUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-green-600 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 font-bold\",\n                                                                            children: getInitials(((_technicians_find2 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find2 === void 0 ? void 0 : _technicians_find2.fullName) || \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1468,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        ((_technicians_find3 = technicians.find((t)=>t.id === production.assignedUserId)) === null || _technicians_find3 === void 0 ? void 0 : _technicians_find3.fullName) || \"Atanmış\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1467,\n                                                                    columnNumber: 31\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: \"Atanmamış\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1480,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1331,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1330,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? \"cursor-pointer\" : \"\"),\n                                                                onClick: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" ? ()=>handleNotesEdit(production.id, production.notes || \"\") : undefined,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden \",\n                                                                                    children: production.notes || \"-\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1506,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1505,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    children: production.notes\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                    lineNumber: 1512,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                                lineNumber: 1511,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1504,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1489,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1488,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"py-3 px-2 w-[30px] max-w-[30px] \",\n                                                            children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex gap-1 justify-center  w-[50px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleArchive(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-blue-600 hover:text-blue-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1530,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1524,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleDelete(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-red-600 hover:text-red-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                            lineNumber: 1538,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                                lineNumber: 1523,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                            lineNumber: 1521,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, production.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 897,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 849,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                lineNumber: 848,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                        lineNumber: 846,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 842,\n                columnNumber: 7\n            }, this),\n            editingNameId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Ad Soyad D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1557,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Ad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameModalData.name,\n                                            onChange: (e)=>setNameModalData({\n                                                    ...nameModalData,\n                                                    name: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Ad girin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1563,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1559,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Soyad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1574,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: nameModalData.surname,\n                                            onChange: (e)=>setNameModalData({\n                                                    ...nameModalData,\n                                                    surname: e.target.value\n                                                }),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Soyad girin...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1573,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1558,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleNameSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1593,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1592,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleNameCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1601,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1596,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1591,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1556,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1555,\n                columnNumber: 9\n            }, this),\n            editingDateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Onay Tarihi D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1613,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: tempDateValue,\n                            onChange: (e)=>setTempDateValue(e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1614,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleDateSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1622,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1621,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleDateCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1630,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1625,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1620,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1612,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1611,\n                columnNumber: 9\n            }, this),\n            editingTargetDateId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Teslimat Tarihi D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1642,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"date\",\n                            value: tempTargetDateValue,\n                            onChange: (e)=>setTempTargetDateValue(e.target.value),\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1645,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleTargetDateSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1653,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1652,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleTargetDateCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1661,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1656,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1651,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1641,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1640,\n                columnNumber: 9\n            }, this),\n            editingTargetDaysId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Hedef G\\xfcn D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1673,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Ka\\xe7 g\\xfcn sonra teslim edilecek?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1675,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"number\",\n                                    value: tempTargetDaysValue,\n                                    onChange: (e)=>setTempTargetDaysValue(e.target.value),\n                                    min: \"0\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                    placeholder: \"G\\xfcn sayısını girin...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1678,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Onay tarihinden itibaren bu kadar g\\xfcn sonra teslimat tarihi otomatik hesaplanacak.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1674,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleTargetDaysSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1693,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1692,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleTargetDaysCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1701,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1696,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1691,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1672,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1671,\n                columnNumber: 9\n            }, this),\n            editingReplicationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"\\xc7oğaltma Sayısı D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1713,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            type: \"number\",\n                            value: tempReplicationValue,\n                            onChange: (e)=>setTempReplicationValue(e.target.value),\n                            min: \"0\",\n                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                            placeholder: \"\\xc7oğaltma sayısını girin...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1716,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleReplicationSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1726,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1725,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleReplicationCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1734,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1729,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1724,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1712,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1711,\n                columnNumber: 9\n            }, this),\n            editingNotesId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"A\\xe7ıklama D\\xfczenle\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1746,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                            value: notesModalData,\n                            onChange: (e)=>setNotesModalData(e.target.value),\n                            className: \"w-full h-32 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                            placeholder: \"A\\xe7ıklama girin...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1747,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleNotesSave,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1755,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Kaydet\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1754,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleNotesCancel,\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_Calendar_Check_Clock_Package_Pause_Play_RefreshCw_Save_Search_Trash2_Truck_UserCheck_UserX_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"İptal\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1758,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1753,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1745,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1744,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: deleteConfirmId !== null,\n                onOpenChange: cancelDelete,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"\\xdcretimi Sil\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1776,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1774,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelDelete,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1782,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    children: \"Sil\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1785,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1781,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1773,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1772,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: archiveConfirmId !== null,\n                onOpenChange: cancelArchive,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"\\xdcretimi Arşivle\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1796,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi arşive almak istediğinizden emin misiniz? Arşivlenen \\xfcretimler plan arşiv b\\xf6l\\xfcm\\xfcnde g\\xf6r\\xfcnt\\xfclenebilir.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1797,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1795,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelArchive,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1803,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmArchive,\n                                    children: \"Arşivle\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1806,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1802,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1794,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1793,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: assignConfirmId !== null,\n                onOpenChange: cancelTaskAssign,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"G\\xf6rev Alma Onayı\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1815,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu g\\xf6revi almak istediğinizden emin misiniz? G\\xf6rev size atanacak ve s\\xfcre\\xe7 takibi kısmını sadece siz d\\xfczenleyebileceksiniz.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1816,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1814,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelTaskAssign,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1822,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmTaskAssign,\n                                    children: \"G\\xf6revi Al\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                                    lineNumber: 1825,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                            lineNumber: 1821,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                    lineNumber: 1813,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n                lineNumber: 1812,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\uretim\\\\page.tsx\",\n        lineNumber: 691,\n        columnNumber: 5\n    }, this);\n}\n_s(UretimPage, \"xO5dyTApipIzlm7gE7Kg+rQgAd8=\", false, function() {\n    return [\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_9__.useAuthStore,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalR,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent,\n        _hooks_useSignalR__WEBPACK_IMPORTED_MODULE_10__.useSignalREvent\n    ];\n});\n_c = UretimPage;\nvar _c;\n$RefreshReg$(_c, \"UretimPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/uretim/page.tsx\n"));

/***/ })

});