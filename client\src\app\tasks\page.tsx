"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import {
  BarChart3,
  CheckCircle,
  Calendar,
  AlertTriangle,
  Clock,
  TrendingUp,
  UserCheck,
} from "lucide-react";
import { useState, useEffect, useCallback } from "react";
import { useAuthStore } from "@/stores/authStore";

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://104.248.243.108/api";

interface DashboardStats {
  totalTasks: number;
  assignedTasks: number;
  unassignedTasks: number;
  completedTasks: number;
  technicianStats: Array<{
    technicianId: string;
    taskCount: number;
    completedCount: number;
    averageCompletionDays: number;
  }>;
  tasksByStatus: Array<{
    status: string;
    count: number;
  }>;
  overdueTasks: number;
}

interface TechnicianStatsData {
  assignedTasks: number;
  completedTasks: number;
  inProgressTasks: number;
  averageCompletionDays: number;
  onTimeCompletions: number;
  lateCompletions: number;
}

export default function AdminDashboard() {
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(
    null
  );
  const [technicianStats, setTechnicianStats] =
    useState<TechnicianStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();

  const fetchDashboardStats = useCallback(async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/Production/dashboard-stats`
      );
      if (response.ok) {
        const data = await response.json();
        setDashboardStats(data);
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    }
  }, [API_BASE_URL]);

  const fetchTechnicianStats = useCallback(async () => {
    if (user?.role === "Technician" && user?.id) {
      try {
        const response = await fetch(
          `${API_BASE_URL}/Production/technician-stats/${user.id}`
        );
        if (response.ok) {
          const data = await response.json();
          setTechnicianStats(data);
        }
      } catch (error) {
        console.error("Error fetching technician stats:", error);
      }
    }
  }, [API_BASE_URL, user?.role, user?.id]);

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchDashboardStats(), fetchTechnicianStats()]);
      setLoading(false);
    };

    loadData();
  }, [user, fetchDashboardStats, fetchTechnicianStats]);

  const getStatCards = () => {
    if (user?.role === "Admin") {
      return [
        {
          title: "Toplam Görevler",
          value: dashboardStats?.totalTasks?.toString() || "0",
          icon: BarChart3,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          title: "Atanan Görevler",
          value: dashboardStats?.assignedTasks?.toString() || "0",
          icon: UserCheck,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          title: "Atanmayan Görevler",
          value: dashboardStats?.unassignedTasks?.toString() || "0",
          icon: AlertTriangle,
          color: "text-orange-600",
          bgColor: "bg-orange-50",
        },
        {
          title: "Geciken Görevler",
          value: dashboardStats?.overdueTasks?.toString() || "0",
          icon: Clock,
          color: "text-red-600",
          bgColor: "bg-red-50",
        },
      ];
    } else if (user?.role === "Technician") {
      return [
        {
          title: "Atanan Görevler",
          value: technicianStats?.assignedTasks?.toString() || "0",
          icon: UserCheck,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          title: "Tamamlanan",
          value: technicianStats?.completedTasks?.toString() || "0",
          icon: CheckCircle,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          title: "Devam Eden",
          value: technicianStats?.inProgressTasks?.toString() || "0",
          icon: Clock,
          color: "text-orange-600",
          bgColor: "bg-orange-50",
        },
        {
          title: "Ort. Tamamlama",
          value: `${
            technicianStats?.averageCompletionDays?.toFixed(1) || "0"
          } gün`,
          icon: TrendingUp,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
        },
      ];
    } else {
      return [
        {
          title: "Toplam Görevler",
          value: dashboardStats?.totalTasks?.toString() || "0",
          icon: BarChart3,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          title: "Tamamlanan",
          value: dashboardStats?.completedTasks?.toString() || "0",
          icon: CheckCircle,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          title: "Devam Eden",
          value: dashboardStats?.assignedTasks?.toString() || "0",
          icon: Calendar,
          color: "text-orange-600",
          bgColor: "bg-orange-50",
        },
        {
          title: "Geciken",
          value: dashboardStats?.overdueTasks?.toString() || "0",
          icon: AlertTriangle,
          color: "text-red-600",
          bgColor: "bg-red-50",
        },
      ];
    }
  };

  const stats = getStatCards();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Yükleniyor...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">
          OrthoClear üretim yönetim sistemi
          {user?.role === "Technician" && " - Teknisyen Paneli"}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {stat.title}
                    </p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Content based on role */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {user?.role === "Admin" && dashboardStats && (
          <>
            {/* Technician Performance */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Teknisyen Performansı</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardStats.technicianStats
                    .slice(0, 5)
                    .map((tech, index) => (
                      <div
                        key={tech.technicianId}
                        className="flex items-center justify-between"
                      >
                        <div>
                          <p className="text-sm font-medium">
                            Teknisyen {index + 1}
                          </p>
                          <p className="text-xs text-gray-500">
                            {tech.taskCount} görev, {tech.completedCount}{" "}
                            tamamlanan
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {tech.averageCompletionDays.toFixed(1)} gün
                          </p>
                          <p className="text-xs text-gray-500">ortalama</p>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            {/* Task Status Distribution */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Görev Durumu</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {dashboardStats.tasksByStatus.map((status) => (
                    <div
                      key={status.status}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm capitalize">
                        {status.status}
                      </span>
                      <span className="text-sm font-medium">
                        {status.count}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {user?.role === "Technician" && technicianStats && (
          <>
            {/* Performance Metrics */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Performans Metrikleri</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Zamanında Tamamlanan</span>
                    <span className="text-sm font-medium text-green-600">
                      {technicianStats.onTimeCompletions}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Geç Tamamlanan</span>
                    <span className="text-sm font-medium text-red-600">
                      {technicianStats.lateCompletions}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Başarı Oranı</span>
                    <span className="text-sm font-medium">
                      {technicianStats.completedTasks > 0
                        ? Math.round(
                            (technicianStats.onTimeCompletions /
                              technicianStats.completedTasks) *
                              100
                          )
                        : 0}
                      %
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Daily Progress */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Günlük İlerleme</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Tamamlanma Oranı</span>
                    <span className="text-sm font-medium">
                      {technicianStats.assignedTasks > 0
                        ? Math.round(
                            (technicianStats.completedTasks /
                              technicianStats.assignedTasks) *
                              100
                          )
                        : 0}
                      %
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{
                        width: `${
                          technicianStats.assignedTasks > 0
                            ? (technicianStats.completedTasks /
                                technicianStats.assignedTasks) *
                              100
                            : 0
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {user?.role !== "Admin" && user?.role !== "Technician" && (
          <>
            {/* General Statistics */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Son Aktiviteler</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        Üretim süreçleri devam ediyor
                      </p>
                      <p className="text-xs text-gray-500">Anlık durum</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        Görevler teknisyenlere atanıyor
                      </p>
                      <p className="text-xs text-gray-500">Sistem durumu</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>Sistem Bilgisi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Sistem Durumu</span>
                    <span className="text-sm font-medium text-green-600">
                      Aktif
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Son Güncelleme</span>
                    <span className="text-sm font-medium">Bugün</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}
