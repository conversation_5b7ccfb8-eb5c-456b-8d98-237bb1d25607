"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/bitenler/page",{

/***/ "(app-pages-browser)/./src/app/tasks/bitenler/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/tasks/bitenler/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BitenlerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nfunction BitenlerPage() {\n    _s();\n    const [archivedProductions, setArchivedProductions] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [unarchiveConfirmId, setUnarchiveConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const { user } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore)();\n    const fetchArchivedProductions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/archived\"));\n            if (response.ok) {\n                const data = await response.json();\n                setArchivedProductions(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching archived productions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const unarchiveProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id, \"/unarchive\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setArchivedProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error unarchiving production:\", error);\n        }\n        return false;\n    };\n    const handleUnarchive = (id)=>{\n        setUnarchiveConfirmId(id);\n    };\n    const confirmUnarchive = async ()=>{\n        if (unarchiveConfirmId) {\n            const success = await unarchiveProduction(unarchiveConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Üretim başarıyla arşivden çıkarıldı!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Üretim arşivden çıkarılamadı!\");\n            }\n            setUnarchiveConfirmId(null);\n        }\n    };\n    const cancelUnarchive = ()=>{\n        setUnarchiveConfirmId(null);\n    };\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/User\"));\n            if (response.ok) {\n                const data = await response.json();\n                setUsers(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching users:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"BitenlerPage.useEffect\": ()=>{\n            fetchArchivedProductions();\n            fetchUsers();\n        }\n    }[\"BitenlerPage.useEffect\"], []);\n    const filteredProductions = archivedProductions.filter((p)=>p.name.toLowerCase().includes(searchTerm.toLowerCase()) || p.surname.toLowerCase().includes(searchTerm.toLowerCase()) || p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()));\n    const calculateTargetDays = (targetDate, approvalDate)=>{\n        if (!targetDate || !approvalDate) return null;\n        const target = new Date(targetDate);\n        const approval = new Date(approvalDate);\n        const diffTime = target.getTime() - approval.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const calculateRemainingDays = (targetDate)=>{\n        if (!targetDate) return null;\n        const target = new Date(targetDate);\n        const today = new Date();\n        const diffTime = target.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"tr-TR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    // Son Yapan'ı bulmak için yardımcı fonksiyon\n    function getUserName(userId) {\n        if (!userId) return \"-\";\n        const u = users.find((u)=>u.id === userId);\n        return u ? u.fullName : userId;\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Y\\xfckleniyor...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 w-full mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Arşivlenmiş \\xdcretimler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Arşive alınmış \\xfcretimleri g\\xf6r\\xfcnt\\xfcleyin ve y\\xf6netin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            \"Rapor İndir\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"h-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Toplam Arşiv\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: archivedProductions.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"w-full h-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Hasta adı, soyadı veya notlarda ara...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            children: \"Arşivlenmiş \\xdcretimler\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]\",\n                                                            children: \"Ad Soyad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] \",\n                                                            children: \"Onay Kutuları\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]\",\n                                                            children: \"Onay Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"Hedef G\\xfcn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]\",\n                                                            children: \"Teslimat Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900   w-[40px] max-w-[40px]\",\n                                                            children: \"Tamamlanma Durumu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"cursor-pointer\",\n                                                                                children: \"\\xc7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 280,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: \"\\xc7oğaltma\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] \",\n                                                            children: \"S\\xfcre\\xe7 Takibi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"A\\xe7ıklama\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"Son Yapan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"İşlemler\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredProductions.map((production, index)=>{\n                                                    const remainingDays = calculateRemainingDays(production.targetDate);\n                                                    const targetDaysCount = calculateTargetDays(production.targetDate, production.approvalDate);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50 bg-gray-50/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1 text-center border !w-[1px] !max-w-[1px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-700 !text-[13px] \",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 319,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1  !w-[50px] !max-w-[50px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 cursor-pointer block truncate\",\n                                                                                    children: (()=>{\n                                                                                        const fullName = \"\".concat(production.name, \" \").concat(production.surname);\n                                                                                        return fullName.length > 15 ? \"\".concat(fullName.substring(0, 15), \"...\") : fullName;\n                                                                                    })()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 329,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 328,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: [\n                                                                                                production.name,\n                                                                                                \" \",\n                                                                                                production.surname\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 340,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"mt-1 text-sm \",\n                                                                                            children: production.notes\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 344,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 339,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  !w-[50px] !max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-3 justify-center  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center \",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.pStar,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-blue-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 361,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 360,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 359,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"P*\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 370,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 369,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 358,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.greening,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 378,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 377,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 376,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Yeşil\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 387,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 386,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 375,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 374,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.adminApproval,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 395,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 394,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 393,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Onay\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 404,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 403,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 392,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 391,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[30px] max-w-[30px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.approvalDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: targetDaysCount !== null ? \"\".concat(targetDaysCount, \" g\\xfcn\") : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.targetDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[30px] max-w-[30px] \",\n                                                                children: remainingDays !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm px-2 py-1 rounded \".concat(remainingDays < 0 ? \"bg-red-100 text-red-800\" : remainingDays === 0 ? \"bg-green-100 text-green-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                    children: remainingDays < 0 ? \"\".concat(Math.abs(remainingDays), \" g\\xfcn ge\\xe7\") : remainingDays === 0 ? \"Zamanında\" : \"\".concat(remainingDays, \" g\\xfcn erken\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 437,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 453,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[5px] max-w-[5px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center  \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-center\",\n                                                                        children: production.replication || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 460,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[50px] max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2 justify-center flex-wrap  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.model,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 473,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 471,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Model\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 482,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 481,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.platePressing,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-yellow-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 490,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 489,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 488,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Plak Basma\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 499,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 498,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.fineCut,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-orange-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 507,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 506,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 505,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"İnce Kesim\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 516,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 515,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 504,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.packaging,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 524,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 523,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 522,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Paketleme\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 533,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 532,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 520,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.shipping,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-red-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 541,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 540,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 539,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Kargo\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 550,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 549,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden cursor-help\",\n                                                                                        children: production.notes || \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 563,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    className: \"max-w-xs\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: production.notes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 569,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: getUserName(production.assignedUserId)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[30px] max-w-[30px] \",\n                                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-1 justify-center  w-[50px]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleUnarchive(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-green-600 hover:text-green-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 592,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 586,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, production.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this),\n                                    filteredProductions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: searchTerm ? \"Arama kriterleriyle eşleşen arşivlenmiş üretim bulunamadı.\" : \"Henüz arşivlenmiş üretim bulunmamaktadır.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: unarchiveConfirmId !== null,\n                onOpenChange: cancelUnarchive,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi arşivden \\xe7ıkarmak istediğinizden emin misiniz? \\xdcretim aktif listesine geri d\\xf6nd\\xfcr\\xfclecek.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelUnarchive,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmUnarchive,\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(BitenlerPage, \"5jgni1wt2lO1x4YpSgPr9zA44dw=\", false, function() {\n    return [\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore\n    ];\n});\n_c = BitenlerPage;\nvar _c;\n$RefreshReg$(_c, \"BitenlerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/bitenler/page.tsx\n"));

/***/ })

});