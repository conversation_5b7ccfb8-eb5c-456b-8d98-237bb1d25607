"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/tasks/bitenler/page",{

/***/ "(app-pages-browser)/./src/app/tasks/bitenler/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/tasks/bitenler/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BitenlerPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Download,Search,Undo!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/undo.js\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _stores_authStore__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/stores/authStore */ \"(app-pages-browser)/./src/stores/authStore.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\nfunction BitenlerPage() {\n    _s();\n    const [archivedProductions, setArchivedProductions] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(\"\");\n    const [unarchiveConfirmId, setUnarchiveConfirmId] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const { user } = (0,_stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore)();\n    const fetchArchivedProductions = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/archived\"));\n            if (response.ok) {\n                const data = await response.json();\n                setArchivedProductions(data);\n            }\n        } catch (error) {\n            console.error(\"Error fetching archived productions:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const unarchiveProduction = async (id)=>{\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/Production/\").concat(id, \"/unarchive\"), {\n                method: \"POST\"\n            });\n            if (response.ok) {\n                setArchivedProductions((prev)=>prev.filter((p)=>p.id !== id));\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error unarchiving production:\", error);\n        }\n        return false;\n    };\n    const handleUnarchive = (id)=>{\n        setUnarchiveConfirmId(id);\n    };\n    const confirmUnarchive = async ()=>{\n        if (unarchiveConfirmId) {\n            const success = await unarchiveProduction(unarchiveConfirmId);\n            if (success) {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Üretim başarıyla arşivden çıkarıldı!\");\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Üretim arşivden çıkarılamadı!\");\n            }\n            setUnarchiveConfirmId(null);\n        }\n    };\n    const cancelUnarchive = ()=>{\n        setUnarchiveConfirmId(null);\n    };\n    const fetchUsers = async ()=>{\n        try {\n            const data = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.apiService.getAllUsers();\n            setUsers(data);\n        } catch (error) {\n            console.error(\"Error fetching users:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"BitenlerPage.useEffect\": ()=>{\n            fetchArchivedProductions();\n            fetchUsers();\n        }\n    }[\"BitenlerPage.useEffect\"], []);\n    const filteredProductions = archivedProductions.filter((p)=>p.name.toLowerCase().includes(searchTerm.toLowerCase()) || p.surname.toLowerCase().includes(searchTerm.toLowerCase()) || p.notes && p.notes.toLowerCase().includes(searchTerm.toLowerCase()));\n    const calculateTargetDays = (targetDate, approvalDate)=>{\n        if (!targetDate || !approvalDate) return null;\n        const target = new Date(targetDate);\n        const approval = new Date(approvalDate);\n        const diffTime = target.getTime() - approval.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const calculateRemainingDays = (targetDate)=>{\n        if (!targetDate) return null;\n        const target = new Date(targetDate);\n        const today = new Date();\n        const diffTime = target.getTime() - today.getTime();\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        return diffDays;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"tr-TR\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    // Son Yapan'ı bulmak için yardımcı fonksiyon\n    function getUserName(userId) {\n        if (!userId) return \"-\";\n        const u = users.find((u)=>u.id === userId);\n        return u ? u.fullName : userId;\n    }\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-lg\",\n                children: \"Y\\xfckleniyor...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 w-full mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900\",\n                                children: \"Arşivlenmiş \\xdcretimler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Arşive alınmış \\xfcretimleri g\\xf6r\\xfcnt\\xfcleyin ve y\\xf6netin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            \"Rapor İndir\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-60\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                            className: \"h-24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Toplam Arşiv\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: archivedProductions.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"w-full h-24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Hasta adı, soyadı veya notlarda ara...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                            children: \"Arşivlenmiş \\xdcretimler\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"overflow-x-auto max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-6 py-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"w-full \",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"border-b\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 !w-[1px] !max-w-[1px] \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-1 font-medium text-gray-900  w-[30px] max-w-[30px]\",\n                                                            children: \"Ad Soyad\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900  w-[30px] max-w-[30px] \",\n                                                            children: \"Onay Kutuları\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 !w-[20px] !max-w-[20px]\",\n                                                            children: \"Onay Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"Hedef G\\xfcn\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[35px] max-w-[35px]\",\n                                                            children: \"Teslimat Tarihi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900   w-[40px] max-w-[40px]\",\n                                                            children: \"Tamamlanma Durumu\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[5px] max-w-[5px] \",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"cursor-pointer\",\n                                                                                children: \"\\xc7\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                children: \"\\xc7oğaltma\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 275,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[25px] max-w-[25px] \",\n                                                            children: \"S\\xfcre\\xe7 Takibi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"A\\xe7ıklama\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-left text-sm py-3 px-2 font-medium text-gray-900 w-[40px] max-w-[40px]\",\n                                                            children: \"Son Yapan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"text-center text-sm py-3 px-2 font-medium text-gray-900 w-[30px] max-w-[30px]\",\n                                                            children: \"İşlemler\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredProductions.map((production, index)=>{\n                                                    const remainingDays = calculateRemainingDays(production.targetDate);\n                                                    const targetDaysCount = calculateTargetDays(production.targetDate, production.approvalDate);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b hover:bg-gray-50 bg-gray-50/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1 text-center border !w-[1px] !max-w-[1px]\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-gray-700 !text-[13px] \",\n                                                                    children: index + 1\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-1  !w-[50px] !max-w-[50px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                asChild: true,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium text-gray-900 cursor-pointer block truncate\",\n                                                                                    children: (()=>{\n                                                                                        const fullName = \"\".concat(production.name, \" \").concat(production.surname);\n                                                                                        return fullName.length > 15 ? \"\".concat(fullName.substring(0, 15), \"...\") : fullName;\n                                                                                    })()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 320,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                className: \"max-w-xs\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"font-medium\",\n                                                                                            children: [\n                                                                                                production.name,\n                                                                                                \" \",\n                                                                                                production.surname\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 332,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"mt-1 text-sm \",\n                                                                                            children: production.notes\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 336,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 330,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  !w-[50px] !max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-3 justify-center  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center \",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.pStar,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-blue-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 353,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 352,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 351,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"P*\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 362,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 361,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.greening,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 370,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 369,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Yeşil\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 379,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 378,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 367,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 366,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.adminApproval,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 387,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 386,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 385,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Onay\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 396,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 395,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 383,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[30px] max-w-[30px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.approvalDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 404,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: targetDaysCount !== null ? \"\".concat(targetDaysCount, \" g\\xfcn\") : \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[20px] max-w-[20px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: formatDate(production.targetDate)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[30px] max-w-[30px] \",\n                                                                children: remainingDays !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm px-2 py-1 rounded \".concat(remainingDays < 0 ? \"bg-red-100 text-red-800\" : remainingDays === 0 ? \"bg-green-100 text-green-800\" : \"bg-blue-100 text-blue-800\"),\n                                                                    children: remainingDays < 0 ? \"\".concat(Math.abs(remainingDays), \" g\\xfcn ge\\xe7\") : remainingDays === 0 ? \"Zamanında\" : \"\".concat(remainingDays, \" g\\xfcn erken\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"-\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2   w-[5px] max-w-[5px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-center  \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-center\",\n                                                                        children: production.replication || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2  w-[50px] max-w-[50px]  \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2 justify-center flex-wrap  \",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.model,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-green-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 465,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 464,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 463,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Model\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 474,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 473,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.platePressing,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-yellow-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 482,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 481,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 480,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Plak Basma\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 491,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 490,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 479,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.fineCut,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-orange-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 499,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 498,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 497,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"İnce Kesim\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 508,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 507,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 496,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 495,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.packaging,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-purple-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 516,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 515,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 514,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Paketleme\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 525,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 524,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 512,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center\",\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                type: \"checkbox\",\n                                                                                                checked: production.shipping,\n                                                                                                disabled: true,\n                                                                                                className: \"w-4 h-4 text-red-600\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                                lineNumber: 533,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 532,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 531,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            children: \"Kargo\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                            lineNumber: 542,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 541,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                lineNumber: 530,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 \",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipProvider, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm text-gray-600 line-clamp-2 flex-1 overflow-hidden cursor-help\",\n                                                                                        children: production.notes || \"-\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 555,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 554,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                production.notes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                                                                    className: \"max-w-xs\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        children: production.notes\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                        lineNumber: 561,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                                    lineNumber: 560,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 553,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[40px] max-w-[40px] \",\n                                                                children: getUserName(production.assignedUserId)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"py-3 px-2 w-[30px] max-w-[30px] \",\n                                                                children: (user === null || user === void 0 ? void 0 : user.role) === \"Admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-1 justify-center  w-[50px]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>handleUnarchive(production.id),\n                                                                        className: \"h-8 w-8 p-0 text-green-600 hover:text-green-700\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Download_Search_Undo_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, production.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    filteredProductions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500\",\n                                        children: searchTerm ? \"Arama kriterleriyle eşleşen arşivlenmiş üretim bulunamadı.\" : \"Henüz arşivlenmiş üretim bulunmamaktadır.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: unarchiveConfirmId !== null,\n                onOpenChange: cancelUnarchive,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogDescription, {\n                                    children: \"Bu \\xfcretimi arşivden \\xe7ıkarmak istediğinizden emin misiniz? \\xdcretim aktif listesine geri d\\xf6nd\\xfcr\\xfclecek.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: cancelUnarchive,\n                                    children: \"İptal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: confirmUnarchive,\n                                    children: \"Arşivden \\xc7ıkar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                    lineNumber: 608,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\OrthoClear\\\\Excel\\\\client\\\\src\\\\app\\\\tasks\\\\bitenler\\\\page.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(BitenlerPage, \"5jgni1wt2lO1x4YpSgPr9zA44dw=\", false, function() {\n    return [\n        _stores_authStore__WEBPACK_IMPORTED_MODULE_7__.useAuthStore\n    ];\n});\n_c = BitenlerPage;\nvar _c;\n$RefreshReg$(_c, \"BitenlerPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/tasks/bitenler/page.tsx\n"));

/***/ })

});